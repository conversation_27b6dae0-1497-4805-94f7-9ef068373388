/**
 * 运费计算API
 */
const ShippingApi = {
  /**
   * 查询运费报价
   * @param {Object} params 查询参数
   * @param {string} params.countryCode 目标国家编码，如 US、GB、DE 等
   * @param {number} params.weight 重量(克)，必须大于0
   * @param {Array<number>} params.categoryIds 商品分类ID列表，可选
   * @param {number} params.length 长度(cm)，可选
   * @param {number} params.width 宽度(cm)，可选
   * @param {number} params.height 高度(cm)，可选
   * @param {string} params.postalCode 邮编，可选
   * @param {string} params.stateProvince 州/省，可选
   * @param {string} params.city 城市，可选
   * @returns {Promise<Array>} 运费方案列表
   */
  getQuote: (params) => {
    // 构建请求参数
    const requestData = {
      countryCode: params.countryCode,
      weight: params.weight
    }

    // 添加可选参数
    if (params.categoryIds && params.categoryIds.length > 0) {
      requestData.categoryIds = params.categoryIds
    }

    if (params.length) {
      requestData.length = params.length
    }

    if (params.width) {
      requestData.width = params.width
    }

    if (params.height) {
      requestData.height = params.height
    }

    if (params.postalCode) {
      requestData.postalCode = params.postalCode
    }

    if (params.stateProvince) {
      requestData.stateProvince = params.stateProvince
    }

    if (params.city) {
      requestData.city = params.city
    }

    return useClientPost('/agent/shipping-calculation/quote', {
      body: requestData,
      custom: {
        showLoading: true,
        auth: true,
        showError: true,
      },
    })
  },

  /**
   * 转换分类数据为API需要的格式
   * @param {Array} categories 前端分类数据
   * @returns {Array<number>} 分类ID列表
   */
  convertCategoriesToIds(categories) {
    if (!Array.isArray(categories) || categories.length === 0) {
      return []
    }

    // 提取子分类ID
    return categories
      .filter(cat => cat.subId)
      .map(cat => cat.subId)
  },

  /**
   * 转换国家数据为API需要的格式
   * @param {Object} country 前端国家数据
   * @returns {string} 国家编码
   */
  convertCountryToCode(country) {
    if (!country) {
      return null
    }

    // 如果直接是字符串，返回字符串
    if (typeof country === 'string') {
      return country
    }

    // 如果是对象，提取code字段
    return country.code || country.countryCode || null
  }
}

export default ShippingApi
