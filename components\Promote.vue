<template>
  <!-- 轮播活动 -->
  <div class="huodong" tabindex="-1">
    <!-- 桌面端轮播 -->
    <q-carousel
      v-model="slide"
      transition-prev="slide-right"
      transition-next="slide-left"
      swipeable
      animated
      arrows
      autoplay
      infinite
      height="140px"
      class="bg-grey-1 rounded-borders gt-xs desktop-carousel"
      tabindex="-1">
      <q-carousel-slide v-for="(group, index) in groupedActivities" :key="index" :name="index + 1" class="column no-wrap q-pa-none">
        <div class="row fit justify-between items-center">
          <q-img v-for="(activity, idx) in group" :key="idx" class="rounded-borders activity-image" :src="activity.imagepath" :alt="activity.title" @click="navigate(activity.link)" />
        </div>
      </q-carousel-slide>
    </q-carousel>

    <!-- 移动端轮播 -->
    <q-carousel
      v-model="mobileSlide"
      transition-prev="slide-right"
      transition-next="slide-left"
      swipeable
      animated
      arrows
      autoplay
      infinite
      height="110px"
      class="bg-grey-1 rounded-borders lt-sm mobile-carousel"
      tabindex="-1">
      <q-carousel-slide v-for="(activity, index) in props.items" :key="index" :name="index + 1" class="column no-wrap q-pa-none">
        <div class="row fit justify-center items-center q-py-none">
          <q-img class="rounded-borders mobile-activity-image" :src="activity.imagepath" :alt="activity.title" @click="navigate(activity.link)" />
        </div>
      </q-carousel-slide>
    </q-carousel>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const props = defineProps({
  // 活动列表
  items: {
    type: Array,
    required: false,
    default: () => [],
  },
});
const router = useRouter();
const slide = ref(1);
const mobileSlide = ref(1);

// 将活动按每4个分组（桌面端）
const groupedActivities = computed(() => {
  const groups = [];
  for (let i = 0; i < props.items.length; i += 4) {
    groups.push(props.items.slice(i, i + 4));
  }
  return groups;
});

const navigate = (path) => {
  router.push(path);
};
</script>

<style lang="scss" scoped>
//轮播活动
.huodong {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  margin-top: 10px;
  overflow: hidden; /* 防止容器出现滚动条 */

  .desktop-carousel {
    overflow: hidden !important; /* 防止出现滚动条 */
    outline: none !important; /* 防止获取焦点时出现轮廓线 */

    :deep(.q-carousel__slide) {
      overflow: hidden !important; /* 确保幻灯片内容不溢出 */
    }
  }

  .activity-image {
    width: 24%;
    height: 130px; /* 固定高度，防止内容溢出 */
    cursor: pointer;
    transition: transform 0.3s ease;
    object-fit: cover; /* 确保图片正确填充空间 */

    &:hover {
      transform: scale(1.03);
    }
  }

  .mobile-carousel {
    overflow: hidden !important; /* 防止出现滚动条 */
    outline: none !important; /* 防止获取焦点时出现轮廓线 */

    :deep(.q-carousel__slide) {
      overflow: hidden !important; /* 确保幻灯片内容不溢出 */
    }
  }

  .mobile-activity-image {
    width: 90%;
    max-width: 400px;
    height: 100px; /* 固定高度，防止内容溢出 */
    cursor: pointer;
    object-fit: cover; /* 确保图片正确填充空间 */
  }
}

@media (max-width: 599px) {
  .huodong {
    padding: 0;
    margin-top: 5px;
    overflow: hidden !important; /* 确保在移动端也不会出现滚动条 */

    .mobile-activity-image {
      height: 100px; /* 小屏幕下稍微减小高度 */
    }
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .huodong {
    .activity-image {
      width: 23.5%;
      height: 120px; /* 平板设备上稍微调整高度 */
    }

    .desktop-carousel {
      height: 130px; /* 调整轮播容器高度 */
    }
  }
}
/* 全局样式，确保轮播组件不会出现滚动条 */
:deep(.q-carousel),
:deep(.q-carousel__slide),
:deep(.q-carousel__slides),
:deep(.q-carousel__slide > .q-carousel__slide-inner) {
  overflow: hidden !important;
  outline: none !important;
}
</style>
