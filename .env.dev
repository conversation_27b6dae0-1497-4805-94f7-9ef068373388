#自定义端口
PORT=3001

VITE_APP_ENV=development
# 开发环境baseUrl
NUXT_PUBLIC_API_BASE=http://localhost:48080/app-api/
SERVER_BASE_URL=http://localhost:48080
# SERVER_BASE_URL=http://*************:48080
SERVER_API_PATH=/app-api
# 域名
NUXT_APP_ORIGIN=http://localhost:5000

PAY_RETURN_URL=https://res.herollgame.com/comm/pay-result.html

#租户ID
TENANT_ID= '4'
#网站域名
WEB_URL='http://localhost:3001'
# 商店名称
SHOP_NAME='BuyGoCn'
# 服务邮箱
SUPPORT_EMAIL =<EMAIL>
#是否开启注册验证
ENABLE_CAPTCHA=true

CAPTCHA_SITE_KEY= '1x00000000000000000000AA'

SK = 'a3f5b7c9d1e3f8a2b4c6d7e9f1a3b5c7'
# 是否隐藏支付来源和支付来源链接
PAY_HIDE = 'true'
PAY_HIDE_URL = 'https://pay.veyena.com/to-pay-local.html'
PAY_RETURN_URL = 'https://pay.veyena.com/pay-result.html'