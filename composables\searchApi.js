const SearchApi = {
  // 搜索商品（分页）
  // 支持关键字搜索，包含平台、排序、价格筛选等参数
  searchProducts: (params) => {
    return useClientGet('/product/search/keyword', {
      params,
      custom: {
        showLoading: true,
      },
    });
  },

  // 查询商品详情
  // 通过商品链接获取商品详情信息
  searchDetailByUrl: (url) => {
    return useClientPost('/product/search/url', {
      params: { url },
      custom: {
        showLoading: true,
      },
    });
  },
  // 通过商品链接获取商品详情信息
  searchDetailById: (id, platform) => {
    return useClientPost('/product/search/detail', {
      params: { id, platform },
      custom: {
        showLoading: true,
      },
    });
  },

  // 获取支持的平台列表
  getSupportedPlatforms: () => {
    return useClientGet('/product/search/platforms');
  },
};
export default SearchApi;
