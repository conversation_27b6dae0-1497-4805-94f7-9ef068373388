<template>
  <q-dialog ref="dialogRef" @hide="onDialogHideHandler" persistent>
    <q-card class="address-list-dialog">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-subtitle1 text-weight-medium">
          <q-icon name="location_on" size="xs" color="primary" class="q-mr-xs" />
          选择收货地址
        </div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section class="q-pt-xs">
        <div v-if="addresses.length === 0" class="text-center q-py-lg">
          <q-icon name="location_off" size="2rem" color="grey-7" />
          <div class="text-grey-7 q-mt-sm">您还没有添加收货地址</div>
        </div>

        <div v-else class="row q-col-gutter-md">
          <div v-for="address in addresses" :key="address.id" class="col-12 col-md-6">
            <q-card flat bordered class="address-card cursor-pointer" :class="{ 'selected-address': selectedAddress && selectedAddress.id === address.id }" @click="selectAddress(address)">
              <q-card-section>
                <div class="row items-center justify-between q-mb-sm">
                  <div class="text-subtitle1 text-weight-medium">
                    <q-icon name="home" size="xs" color="primary" class="q-mr-xs" v-if="address.title === '家'" />
                    <q-icon name="business" size="xs" color="primary" class="q-mr-xs" v-else-if="address.title === '公司'" />
                    <q-icon name="place" size="xs" color="primary" class="q-mr-xs" v-else />
                    {{ address.title || address.name }}
                  </div>
                  <div>
                    <q-badge v-if="address.defaultStatus" color="primary" class="q-mr-sm">默认</q-badge>
                  </div>
                </div>

                <div class="address-info">
                  <div class="row items-center q-mb-xs">
                    <q-icon name="person" size="xs" color="grey-7" class="q-mr-xs" />
                    <span>{{ address.name }}</span>
                  </div>
                  <div class="row items-center q-mb-xs">
                    <q-icon name="phone" size="xs" color="grey-7" class="q-mr-xs" />
                    <span>{{ address.phoneCode }} {{ address.mobile }}</span>
                  </div>
                  <div class="row items-start q-mb-xs">
                    <q-icon name="home" size="xs" color="grey-7" class="q-mr-xs q-mt-xs" />
                    <div class="address-detail">
                      <div>{{ address.detailAddress }}</div>
                      <div>{{ address.cityName || getCityName(address.city) }}, {{ address.stateName || getStateName(address.state) }}</div>
                      <div>{{ address.postCode }}, {{ getCountryName(address.countryCode) }}</div>
                    </div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card-section>

      <q-card-actions align="right" class="q-px-md q-pb-md">
        <q-btn flat label="添加新地址" color="primary" @click="$emit('add-address')" />
        <q-space />
        <q-btn flat label="取消" color="grey-7" v-close-popup />
        <q-btn flat label="确认" color="primary" :disable="!selectedAddress" @click="confirmAddress" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { useDialogPluginComponent } from 'quasar';
import AddressApi from '~/composables/addressApi';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  addresses: {
    type: Array,
    required: true,
    default: () => [],
  },
  initialSelected: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits([...useDialogPluginComponent.emits, 'update:modelValue', 'select']);

const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();

// 选中的地址
const selectedAddress = ref(props.initialSelected);

// 国家、州/省、城市数据缓存
const countriesCache = ref([]);
const statesCache = ref({});
const citiesCache = ref({});

// 初始化
onMounted(async () => {
  await loadCountries();
});

// 加载国家列表
async function loadCountries() {
  try {
    const { code, data } = await AddressApi.getCountry();
    if (code === 0 && data) {
      countriesCache.value = data;
    }
  } catch (error) {
    console.error('获取国家列表失败:', error);
  }
}

// 获取国家名称
function getCountryName(countryCode) {
  const country = countriesCache.value.find((c) => c.iso2 === countryCode);
  return country ? country.name : countryCode;
}

// 获取州/省名称
function getStateName(stateId) {
  if (!stateId) return '';

  // 遍历所有州/省缓存
  for (const countryId in statesCache.value) {
    const state = statesCache.value[countryId].find((s) => s.id === stateId);
    if (state) return state.name;
  }

  return stateId;
}

// 获取城市名称
function getCityName(cityId) {
  if (!cityId) return '';

  // 遍历所有城市缓存
  for (const stateId in citiesCache.value) {
    const city = citiesCache.value[stateId].find((c) => c.id === cityId);
    if (city) return city.name;
  }

  return cityId;
}

// 选择地址
function selectAddress(address) {
  selectedAddress.value = address;
}

// 确认地址选择
function confirmAddress() {
  emit('select', selectedAddress.value);
  onDialogOK();
}

// 监听modelValue变化
watch(
  () => props.modelValue,
  (val) => {
    if (val && dialogRef.value) {
      dialogRef.value.show();
    } else if (dialogRef.value) {
      dialogRef.value.hide();
    }
  }
);

// 监听对话框隐藏
function onDialogHideHandler() {
  emit('update:modelValue', false);
}

// 监听初始选中地址变化
watch(
  () => props.initialSelected,
  (val) => {
    selectedAddress.value = val;
  }
);

// 暴露方法
defineExpose({
  show: () => {
    dialogRef.value.show();
  },
  hide: () => {
    dialogRef.value.hide();
  },
});
</script>

<style lang="scss" scoped>
.address-list-dialog {
  width: 100%;
  max-width: 700px;
  border-radius: 8px;

  .q-card-section {
    padding: 16px 16px 8px 16px;
  }

  .text-subtitle1 {
    font-size: 1rem;
    font-weight: 500;
  }

  .address-card {
    height: 100%;
    transition: all 0.2s ease;
    border-radius: 8px;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    &.selected-address {
      border: 2px solid #1976d2;
      background-color: rgba(25, 118, 210, 0.05);
    }

    .address-info {
      font-size: 0.9rem;
      line-height: 1.4;

      .address-detail {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  // 移动端适配
  @media (max-width: 599px) {
    max-width: 95vw;

    .q-card-section {
      padding: 12px 12px 6px 12px;
    }

    .text-subtitle1 {
      font-size: 0.95rem;
    }
  }
}
</style>
