# 支付密码功能更新说明

## 更新概述

根据全球短信发送的不稳定性考虑，我们对支付密码功能进行了重要更新，将验证方式从短信验证码改为邮件验证码，并简化了设置流程。

## 主要变更

### 1. 验证方式变更

**变更前：**
- 首次设置支付密码需要短信验证码
- 重置支付密码需要短信验证码
- 修改支付密码需要当前密码验证

**变更后：**
- 首次设置支付密码无需验证码，直接设置
- 重置支付密码需要邮件验证码（仅在忘记密码时使用）
- 修改支付密码需要当前密码验证（无变化）

### 2. 用户界面更新

#### 2.1 重置密码弹窗
- 将"手机号"输入框改为"邮箱地址"输入框
- 将"短信验证码"改为"邮件验证码"
- 更新相关提示文本和验证规则

#### 2.2 设置流程简化
- 首次设置支付密码时不再跳转到重置流程
- 直接在设置弹窗中完成密码设置

### 3. API接口调整

#### 3.1 新增接口
```javascript
// 发送支付密码重置邮件验证码
sendPayPasswordEmailCode: (data) => {
  return useClientPost('/member/auth/send-mail-code', {
    body: {
      ...data,
      scene: 5, // 支付密码重置场景
    },
    // ...
  });
}
```

#### 3.2 移除接口
```javascript
// 已移除：发送支付密码相关短信验证码
// sendPayPasswordSmsCode()
```

#### 3.3 修改接口
```javascript
// 设置支付密码 - 移除验证码参数
setPayPassword: (data) => {
  // 现在只需要 payPassword 参数，无需验证码
}

// 重置支付密码 - 参数从 mobile 改为 email
resetPayPassword: (data) => {
  // 参数：{ email, code, payPassword }
  // 之前：{ mobile, code, payPassword }
}
```

### 4. 国际化文本更新

#### 4.1 中文文本 (locales/zh.json)
```json
{
  "fields": {
    "email": "邮箱地址",
    "emailCode": "邮件验证码"
  },
  "validations": {
    "emailRequired": "请输入邮箱地址",
    "emailInvalid": "请输入正确的邮箱地址",
    "emailCodeRequired": "请输入验证码"
  },
  "messages": {
    "emailCodeSent": "验证码已发送到您的邮箱",
    "resetTip": "请输入您的邮箱地址，我们将发送验证码用于重置支付密码"
  }
}
```

#### 4.2 英文文本 (locales/en.json)
```json
{
  "fields": {
    "email": "Email Address",
    "emailCode": "Email Verification Code"
  },
  "validations": {
    "emailRequired": "Please enter email address",
    "emailInvalid": "Please enter a valid email address",
    "emailCodeRequired": "Please enter verification code"
  },
  "messages": {
    "emailCodeSent": "Verification code sent to your email",
    "resetTip": "Please enter your email address, we will send a verification code to reset your payment password"
  }
}
```

## 技术实现细节

### 1. 前端变更

#### 1.1 表单数据结构
```javascript
// 变更前
const payPasswordResetForm = reactive({
  mobile: '',
  code: '',
  newPassword: '',
  confirmPassword: '',
});

// 变更后
const payPasswordResetForm = reactive({
  email: '',
  code: '',
  newPassword: '',
  confirmPassword: '',
});
```

#### 1.2 状态管理
```javascript
// 变更前
const sendingSmsCode = ref(false);
const smsCountdown = ref(0);

// 变更后
const sendingEmailCode = ref(false);
const emailCountdown = ref(0);
```

#### 1.3 验证规则
```javascript
// 邮箱验证规则
:rules="[
  (val) => !!val || '请输入邮箱地址', 
  (val) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) || '请输入正确的邮箱地址'
]"
```

### 2. 后端接口要求

#### 2.1 邮件验证码场景
- 场景码：5（支付密码重置）
- 接口：`POST /member/auth/send-mail-code`
- 参数：`{ email, scene: 5 }`

#### 2.2 重置密码接口
- 接口：`PUT /member/user/reset-pay-password`
- 参数：`{ email, code, payPassword }`

## 用户体验改进

### 1. 简化设置流程
- 首次设置支付密码更加便捷，无需额外验证
- 减少用户操作步骤，提升用户体验

### 2. 提高可靠性
- 邮件验证比短信验证更稳定可靠
- 避免因短信服务问题导致的用户体验问题

### 3. 保持安全性
- 重置密码仍需邮件验证码验证
- 修改密码仍需当前密码验证
- 余额支付仍需支付密码验证

## 兼容性说明

### 1. 向后兼容
- 已设置支付密码的用户不受影响
- 现有的修改密码功能保持不变
- 余额支付流程保持不变

### 2. 数据迁移
- 无需数据库结构变更
- 无需用户数据迁移
- 现有用户状态保持不变

## 测试建议

### 1. 功能测试
- [ ] 首次设置支付密码（无验证码）
- [ ] 修改支付密码（需当前密码）
- [ ] 重置支付密码（需邮件验证码）
- [ ] 余额支付（需支付密码）

### 2. 验证码测试
- [ ] 邮件验证码发送
- [ ] 邮件验证码验证
- [ ] 验证码倒计时功能
- [ ] 验证码重发功能

### 3. 边界测试
- [ ] 邮箱格式验证
- [ ] 密码格式验证
- [ ] 网络异常处理
- [ ] 错误提示显示

## 部署注意事项

1. **邮件服务配置**：确保邮件服务正常工作
2. **场景码配置**：确认支付密码重置场景码为5
3. **接口测试**：部署前测试所有相关接口
4. **用户通知**：可考虑通知用户功能更新

## 总结

本次更新主要解决了短信验证的不稳定性问题，同时简化了用户操作流程。通过使用邮件验证码替代短信验证码，提高了功能的可靠性和用户体验。所有变更都保持了向后兼容性，不会影响现有用户的使用。
