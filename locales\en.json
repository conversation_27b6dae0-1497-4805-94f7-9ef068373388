{"__section_common": "==================== Top Navigation and Common Elements ====================", "common": {"cancel": "Cancel", "confirm": "Confirm", "close": "Close"}, "__section_nav": "==================== Top Navigation and Common Elements ====================", "announcement": "<PERSON>.", "chinaTime": "China Time", "home": "Home", "hotProducts": "Hot", "transport": "Transport", "guide": "Guide", "helpCenter": "Help", "bulkPurchase": "Bulk", "notes": "Note", "loginRegister": "Login/Register", "login": "<PERSON><PERSON>", "register": "Register", "cart": "<PERSON><PERSON>", "homeTitle": "Professional Platform for Purchasing Chinese Goods", "__section_steps": "==================== Process Steps ====================", "step1": "Place Order & Payment", "step2": "Procurement & Storage", "step3": "Submit for Shipping", "step4": "Receive Goods", "__section_home": "==================== Homepage Module Titles ====================", "module1Title": "Featured Recommendations", "module2Title": "Trending Purchases", "module3Title": "Popular Topics", "more": "More", "searchPlaceholder": "Search products or links", "ourAdvantages": "Our Advantages", "featuredTopics": "Featured Topics", "__section_user": "==================== User Related ====================", "mobileApp": "Mobile App", "wechatOfficial": "WeChat Official", "pack": "Welcome Pack", "memberCenter": "Member", "message": "Messages", "favorite": "Favorites", "top": "Top", "__section_features": "==================== Features ====================", "features": ["One-stop Service", "More Professional", "More Efficient", "Faster"], "__section_sizeGuide": "==================== Size Guide ====================", "sizeGuide": {"title": "Size Guide", "description": "Choose the right size for easier shopping", "toolTitle": "Size Chart Tool", "toolDescription": "Select unit, country and clothing type to view detailed size charts", "unit": "Unit", "country": "Country/Region", "category": "Clothing Type", "notice": "Please note that this is only a rough conversion table, as sizes may vary between different brands and products. It is recommended to check the detailed size chart of the product before purchasing and choose the appropriate size based on your actual measurements.", "units": {"cm": "cm", "in": "in"}, "categories": {"womenTop": "Women's Top", "menTop": "Men's Top", "womenPants": "Women's Pants", "menPants": "Men's Pants", "womenShoes": "Women's Shoes", "menShoes": "Men's Shoes", "ring": "Ring"}, "countries": {"US": "United States", "UK": "United Kingdom", "EU": "European Union", "JP": "Japan", "AU": "Australia", "RU": "Russia", "BR": "Brazil", "IN": "India", "CA": "Canada", "KR": "South Korea", "NZ": "New Zealand", "DE": "Germany", "FR": "France", "SW": "Sweden"}, "howToUse": {"title": "How to Use Size Guide", "step1": {"title": "Select Unit", "description": "Choose your preferred measurement unit: centimeters (cm) or inches (in)"}, "step2": {"title": "Select Country/Region", "description": "Choose the sizing standard you want to reference, as different countries may have different sizing standards"}, "step3": {"title": "Select Clothing Type", "description": "Choose the appropriate clothing category based on the type of product you want to purchase"}, "step4": {"title": "View Size Chart", "description": "Refer to the data in the table and choose the appropriate size based on your body measurements"}}, "measureGuide": {"title": "Measurement Guide", "tip1": {"title": "Use a Soft Tape Measure", "description": "Use a flexible tape measure for measurements, ensuring it fits snugly against your body without being too tight"}, "tip2": {"title": "Wear Appropriate Undergarments", "description": "Wear the undergarments you normally wear when measuring to get the most accurate results"}, "tip3": {"title": "Choose the Right Time", "description": "It's recommended to measure at the same time of day to avoid variations due to changes in body condition"}}, "important": {"title": "Important Notes", "note1": "Sizes may vary between different brands. It's recommended to refer to the size chart on the product detail page first", "note2": "If your measurements fall between two sizes, it's recommended to choose the larger size", "note3": "For clothing made with stretch fabrics, you can choose a relatively fitted size", "note4": "If you have any questions, it's recommended to contact customer service for professional sizing advice"}}, "__section_footer": "==================== Footer Navigation ====================", "footer": {"title1": "Shopping Guide", "links1": ["Getting Started", "Place Order", "Shipping Order"], "title2": "Payment Info", "links2": ["Credit Card", "Debit Card", "<PERSON><PERSON>", "WeChat Pay", "Alipay"], "title3": "Shipping Info", "links3": ["Shipping Methods", "Customs Tax", "Prohibited Items", "Other Notes"], "title4": "After-Sales", "links4": ["Storage Policy", "Return Policy"], "title5": "About Us", "links5": ["Contact Us", "Join Us", "Business"]}, "__section_images": "==================== Image Resources ====================", "topAdImage": "/src/assets/upload/topAd-en.png", "logoImage": "~/assets/images/logo.png", "searchBgImage": "/src/assets/images/searchBgImage.png", "__section_labels": "==================== Form Labels ====================", "label": {"email": "Email", "password": "Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "loginWithThird": "<PERSON><PERSON> with third-party", "noAccount": "No Account?", "registerNow": "Register Now", "username": "Username", "agree": "I have read and agree", "userAgreement": "User Terms", "and": "and", "privatePolicy": "Privacy Policy", "haveAccount": "Have Account?", "loginNow": "Login Now", "verifyCode": "Verification Code", "newPassword": "New Password"}, "__section_placeholders": "==================== Form Placeholders ====================", "placeholder": {"email": "Please enter your email", "password": "Please enter your password", "username": "Please enter your username", "verifyCode": "Please enter verification code", "newPassword": "Please enter new password"}, "__section_titles": "==================== Page Titles ====================", "title": {"login": "<PERSON><PERSON>", "register": "Register", "resetPassword": "Reset Password", "userAgreement": "User Agreement", "privacyPolicy": "Privacy Policy", "cookiePolicy": "<PERSON><PERSON>"}, "__section_buttons": "==================== Button Text ====================", "button": {"login": "<PERSON><PERSON>", "register": "Register", "myCenter": "User Center", "myOrders": "My Orders", "myMessage": "My Messages", "logout": "Logout", "sendCode": "Send Code", "resend": "Resend", "resetPassword": "Reset Password"}, "__section_validations": "==================== Form Validations ====================", "validations": {"required": "This field is required.", "email": "Invalid email format.", "minLength": "This field must have at least {min} characters.", "maxLength": "This field must have at least {max} characters."}, "__section_notify": "==================== Notifications ====================", "notify": {"registerSuccess": "Register Success!", "loginSuccess": "Login Success!", "verifyCodeSent": "Verification code sent!", "addedToWishlist": "Added to wishlist", "addToWishlistFailed": "Failed to add to wishlist", "addedToCart": "Added to cart", "addToCartFailed": "Failed to add to cart"}, "__section_menu": "==================== Menu Navigation ====================", "menu": {"home": "Home", "account": "My Account", "assets": "Assets", "balance": "Balance", "coupons": "Coupons", "points": "Points", "orders": "Orders", "orderProduct": "Product Orders", "orderTransfer": "Transfer Orders", "warehouse": "Warehouse", "parcel": "My Parcel", "messages": "Messages", "msgInbox": "Inbox", "msgConsult": "Consultations", "wishlist": "Wishlist", "settings": "Settings", "profile": "Profile", "addresses": "Addresses", "security": "Security", "dashboard": "Dashboard"}, "__section_account_dashboard": "==================== Account Dashboard ====================", "accountDashboard": {"userAvatar": "User Avatar", "respectedUser": "Valued Customer", "editProfile": "Edit Profile", "securitySettings": "Security Settings", "accountBalance": "Account Balance (CNY)", "recharge": "Recharge", "myPoints": "My Points", "view": "View", "availableCoupons": "Available Coupons", "pendingTasks": "Pending Tasks", "noPendingTasks": "No pending tasks", "myMessages": "My Messages", "markAllAsRead": "<PERSON> as <PERSON>", "clearAllMessages": "Clear All Messages", "noMessages": "No messages", "viewAll": "View All", "quickAccess": "Quick Access", "recentOrders": "Recent Orders", "noOrders": "No orders", "goShopping": "Go Shopping", "orderNumber": "Order #: ", "viewAllOrders": "View All Orders", "shortcuts": {"myOrders": "My Orders", "myTransfers": "My Transfers", "shippingAddresses": "Shipping Addresses", "myWishlist": "My Wishlist", "accountBalance": "Account <PERSON><PERSON>", "coupons": "Coupons", "customerService": "Customer Service"}, "errors": {"getUserInfoFailed": "Failed to get user information", "getWalletFailed": "Failed to get wallet information", "getNumDataFailed": "Failed to get count data", "getRecentOrdersFailed": "Failed to get recent orders", "getPendingTransfersFailed": "Failed to get pending transfers", "getMessagesFailed": "Failed to get messages"}, "weekdays": {"sunday": "Sun", "monday": "Mon", "tuesday": "<PERSON><PERSON>", "wednesday": "Wed", "thursday": "<PERSON>hu", "friday": "<PERSON><PERSON>", "saturday": "Sat"}, "orderStatus": {"pending": "Pending", "unpaid": "Unpaid", "unshipped": "Processing", "shipped": "Shipped", "completed": "Completed", "closed": "Closed", "unknown": "Unknown"}, "pendingTasksDesc": {"unpaidOrders": "orders to pay", "unpaidOrdersDesc": "Please complete payment soon to avoid order cancellation", "shippedOrders": "orders to receive", "shippedOrdersDesc": "Your packages have been shipped, please check for delivery", "uncommentedOrders": "orders to review", "uncommentedOrdersDesc": "Earn points by reviewing your orders", "pendingTransfers": "transfers to process", "pendingTransfersDesc": "You have transfer orders that need attention"}}, "__section_account_balance": "==================== Balance Page ====================", "accountBalance": {"title": "My Balance", "currentBalance": "Current Balance", "totalIncome": "Total Income", "totalExpense": "Total Expense", "recharge": "Recharge", "tabs": {"all": "All", "income": "Income", "expense": "Expense"}, "loading": "Loading...", "noTransactions": "No transaction records", "pagination": "Total {total} records, Page {current} / {totalPages}", "table": {"transactionType": "Transaction Type", "amount": "Amount", "transactionTime": "Transaction Time"}, "transactionTypes": {"recharge": "Recharge", "withdraw": "Withdraw", "payment": "Payment", "refund": "Refund", "other": "Other"}}, "__section_account_coupons": "==================== Coupons Page ====================", "accountCoupons": {"title": "My Coupons", "tabs": {"available": "Available", "used": "Used", "expired": "Expired", "default": "Coupons"}, "getMore": "Get More", "getCoupons": "Get Coupons", "loading": "Loading...", "noCoupons": "No {status} coupons", "pagination": "Total {total} records, Page {current} / {totalPages}", "errors": {"fetchFailed": "Failed to fetch coupon list", "fetchError": "Error fetching coupon list"}}, "__section_account_points": "==================== Points Page ====================", "accountPoints": {"title": "My Points", "currentPoints": "Current Points", "pointsRules": "Points Rules", "tabs": {"all": "All", "income": "Earned", "expense": "Spent"}, "loading": "Loading...", "noRecords": "No points records", "pagination": "Total {total} records, Page {current} / {totalPages}", "table": {"type": "Type", "change": "Points Change", "time": "Time", "description": "Description"}}, "__section_account_orders": "==================== Orders Page ====================", "accountOrders": {"title": "My Orders", "tabs": {"unpaid": "Unpaid", "purchasing": "Purchasing", "warehoused": "In Warehouse", "additionalPayment": "Additional Payment", "all": "All"}, "search": {"placeholder": "Enter order number"}, "table": {"productDetails": "Product Details", "unitPrice": "Unit Price", "quantity": "Quantity", "amount": "Amount", "orderStatus": "Order Status", "actions": "Actions"}, "orderInfo": {"orderNumber": "Order #:", "totalAmount": "Total Amount:", "orderDetails": "Order Details", "cancelOrder": "Cancel Order", "payNow": "Pay Now", "totalItems": "{count} items, Total:"}, "noOrders": "No orders found", "pagination": "Total {total} records, Page {current} / {totalPages}", "errors": {"emptyOrderId": "Order ID cannot be empty", "cancelFailed": "Failed to cancel order", "cancelError": "Error cancelling order"}, "confirmations": {"cancelTitle": "Cancel Order", "cancelMessage": "Are you sure you want to cancel this order?", "cancelSuccess": "Order cancelled successfully"}, "status": {"unpaid": "Unpaid", "purchasing": "Purchasing", "shipping": "Shipping", "warehoused": "In Warehouse", "cancelled": "Cancelled", "additionalPayment": "Additional Payment", "completed": "Completed", "unknown": "Unknown Status"}}, "__section_help": "==================== Help Center Page ====================", "help": {"title": "Help Center", "searchPlaceholder": "Search help content", "loading": "Loading...", "sidebar": {"homePage": "Help Center Home"}, "contactSupport": {"title": "Couldn't find what you need?", "button": "Contact Support"}, "__section_customer_service": "==================== Customer Service Related ====================", "customerService": {"title": "Customer Service", "description": "Questions? Contact our support team", "onlineStatus": "Online", "offlineStatus": "Offline", "workingHours": "Working Hours: 9:00-18:00 (Mon-Fri)", "pleaseLogin": "Please login to contact customer service", "inputPlaceholder": "Type your question here", "send": "Send", "connecting": "Connecting...", "connectionSuccess": "Connected successfully", "connectionFailed": "Connection failed, please try again later", "newMessage": "New message", "loadMore": "Load more", "noMoreMessages": "No more messages", "tools": {"emoji": "<PERSON><PERSON><PERSON>", "image": "Image", "product": "Product", "order": "Order"}}, "chat": {"loginRequired": "Please login to use customer service", "goToLogin": "<PERSON><PERSON>", "humanConnected": "Connected to customer service", "inputPlaceholder": "Type your question here", "send": "Send", "onlyEmojisAllowed": "For security reasons, only text and emojis are allowed", "backToHome": "Back to Home", "humanService": "Human Support", "aiService": "AI Assistant", "reconnecting": "Reconnecting...", "newMessage": "New message received", "messageRead": "Agent has read your message", "sendMessageError": "Failed to send message", "audioPlayError": "Failed to play audio notification", "websocketReconnectError": "WebSocket reconnect function not available", "status": {"connecting": "Connecting to agent...", "connected": "Connected to agent", "reconnecting": "Reconnecting...", "disconnected": "Connection lost"}, "robot": {"welcome": "Hello, I'm your virtual assistant", "intro": "Please select a topic you'd like to learn about, or click the button below to connect with a human agent", "categories": "FAQ Categories", "popularQuestions": "Popular Questions", "loading": "Loading...", "helpfulQuestion": "Was this answer helpful?", "solved": "Yes, it helped", "notSolved": "No, I need more help", "contactHuman": "Contact Human Agent", "fetchError": "Failed to fetch help center data", "loadError": "Failed to load FAQ details", "thanksFeedback": "Thank you for your feedback!", "connectingHuman": "Connecting to human agent...", "avatar": "AI Assistant", "fetchDataError": "Failed to fetch help center data", "fetchDetailError": "Failed to fetch FAQ details", "processHtmlError": "Error processing HTML content"}, "sendFailed": "Failed to send message, please check your network", "connectionError": "Connection error, please try again later", "messageLimit": "You are sending messages too frequently, please wait a moment", "loadMore": "Load More", "newMessageTip": "New message", "getMessageListFailed": "Failed to get message list", "loadMoreFailed": "Failed to load more messages", "noMoreMessages": "No more messages", "receiveMessageFailed": "Failed to receive message"}, "faq": {"title": "Frequently Asked Questions (FAQ)", "description": "Here are the most common questions from our users. Click on a question to view the detailed answer", "viewMore": "View More FAQs", "pageTitle": "Frequently Asked Questions", "pageDescription": "We've collected the most common questions and answers to help you quickly resolve your inquiries", "noData": "No FAQs available"}, "categories": {"title": "Help Categories", "viewMore": "View More"}, "videoTutorials": {"title": "Video Tutorials"}, "moreFAQ": {"title": "More FAQs"}, "contactDialog": {"title": "Contact Support"}, "buttons": {"close": "Close"}, "pagination": {"info": "Total {total} records, Page {current} / {pages}"}, "search": {"title": "Search Results", "loading": "Searching...", "resultsCount": "Found {count} relevant results", "noResults": "No results found for \"{query}\"", "initialText": "Enter keywords to search", "searchButton": "Search", "browseAllHelp": "Browse all help content", "suggestions": {"title": "Search suggestions:", "checkSpelling": "Check your spelling", "trySimpler": "Try using simpler keywords", "tryRelated": "Try using related keywords", "browseCategories": "Browse help categories to find related content"}}, "detail": {"updatedAt": "Updated on", "views": "views", "feedback": {"title": "Was this article helpful?", "helpful": "Helpful", "notHelpful": "Not Helpful", "helpfulCount": "{count} people found this helpful", "notHelpfulCount": "{count} people found this not helpful"}, "relatedArticles": "Related Articles", "loading": "Loading...", "notFound": "Help content not found", "backToHelp": "Back to Help Center"}, "categoryNotFound": "Category not found", "backToHelp": "Back to Help Center"}, "__section_account_security": "==================== Account Security Page ====================", "security": {"title": "Account Security", "buttons": {"modify": "Modify", "set": "Set", "bind": "Bind", "unbind": "Unbind", "verify": "Verify", "cancel": "Cancel", "confirm": "Confirm"}, "cards": {"passwordManagement": {"title": "Password Management", "loginPassword": {"label": "Login Password", "description": "Used to protect your account information"}, "paymentPassword": {"label": "Payment Password", "description": "Used for wallet payments and withdrawals"}, "autoPayment": {"label": "Auto Payment", "description": "When enabled, wallet balance will be used automatically for additional payments"}}, "accountBinding": {"title": "Account Binding", "email": {"label": "Email", "verified": "Verified", "unverified": "Unverified"}, "mobile": {"label": "Mobile", "notBound": "Not Bound"}, "wechat": {"label": "WeChat"}, "google": {"label": "Google"}, "facebook": {"label": "Facebook"}, "status": {"bound": "Bound", "notBound": "Not Bound"}}}, "dialogs": {"loginPassword": {"title": "Change Login Password", "fields": {"currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password"}, "validations": {"currentPasswordRequired": "Please enter current password", "newPasswordRequired": "Please enter new password", "passwordLength": "Password must be at least 6 characters", "passwordDifferent": "New password cannot be the same as current password", "confirmPasswordRequired": "Please confirm new password", "passwordMatch": "Passwords do not match"}}, "paymentPassword": {"titleModify": "Modify Payment Password", "titleSet": "Set Payment Password"}}}, "__section_bulk_purchase": "==================== Bulk Purchase Page ====================", "bulkPage": {"banner": {"title": "Enterprise Bulk Purchase Solutions", "subtitle": "Providing one-stop overseas product procurement services for your business - save time, effort, and money", "button": "Inquire Now"}, "advantages": {"title": "Our Advantages", "items": {"price": {"title": "Price Advantage", "desc": "Direct connection with overseas suppliers, eliminating intermediaries to save procurement costs"}, "quality": {"title": "Quality Assurance", "desc": "Strict quality control processes to ensure every product meets your requirements"}, "variety": {"title": "Comprehensive Categories", "desc": "Covering electronics, clothing, beauty, baby products and more to meet diverse needs"}, "service": {"title": "Professional Service", "desc": "One-on-one dedicated customer service, following the entire procurement process to solve your concerns"}}}, "scenarios": {"title": "Application Scenarios", "items": {"gifts": {"title": "Corporate Gift Procurement", "desc": "Providing high-quality gift procurement solutions for corporate events, holiday celebrations, and employee benefits, showcasing your company's taste and sincerity."}, "retail": {"title": "Retail Supply Chain", "desc": "Providing stable overseas product sources for e-commerce sellers and physical stores, helping your business expand and increase profits."}, "events": {"title": "Event Supplies Procurement", "desc": "Offering one-stop material procurement services for exhibitions, promotional activities, and team building events to make your activities stand out."}}}, "process": {"title": "Service Process", "steps": {"communication": {"title": "Requirement Communication", "desc": "Submit your procurement needs, and our professional consultants will communicate with you in detail to understand specific requirements"}, "planning": {"title": "Solution Development", "desc": "Based on your needs, we will customize procurement plans, including product selection, price budgeting, etc."}, "confirmation": {"title": "Cooperation Confirmation", "desc": "After confirming the procurement plan, sign a cooperation agreement, pay the deposit, and officially start the procurement process"}, "procurement": {"title": "Product Procurement", "desc": "Our professional team will procure products according to the plan, strictly controlling product quality"}, "logistics": {"title": "Logistics Delivery", "desc": "After product procurement is completed, we will arrange international logistics to ensure safe and timely delivery"}, "completion": {"title": "Acceptance Completion", "desc": "After receiving the products, you will inspect them, pay the balance after confirmation, and complete the entire procurement process"}}}, "faq": {"title": "Frequently Asked Questions", "questions": {"minQuantity": {"question": "What is the minimum quantity requirement for bulk purchases?", "answer": "Our bulk purchase service does not have strict minimum quantity requirements, but we generally recommend purchasing at least 10 pieces per item or a total purchase amount of over 5,000 yuan to better leverage the price advantages of bulk purchasing."}, "payment": {"question": "What payment methods are available for bulk purchases?", "answer": "We support various payment methods, including bank transfers, Alipay, WeChat Pay, etc. We typically use a 'deposit + balance' payment model, with specific ratios determined based on the purchase amount and cooperation situation."}, "delivery": {"question": "How long is the delivery cycle for bulk purchases?", "answer": "The delivery cycle depends on the type and quantity of products being purchased, as well as the supplier's inventory situation. Generally, the entire process takes 15-30 days from order confirmation to product delivery. If you have special requirements, you can communicate with our customer service, and we will try our best to meet your time requirements."}, "quality": {"question": "How do you ensure the quality of procured products?", "answer": "We have strict quality control processes, including supplier screening, product inspection, pre-shipment quality checks, and more. We also provide product quality assurance services. If quality issues are found, returns or exchanges can be processed according to the agreement."}, "sample": {"question": "Can you provide samples?", "answer": "Yes, for large purchase orders, we can provide sample services to let you confirm product quality before placing a large order. Sample costs and shipping fees are usually borne by the customer, but we can deduct these costs from the total amount after the subsequent large order is confirmed."}}}, "contact": {"title": "Contact Us", "subtitle": "If you have any questions or needs regarding our bulk purchase service, please contact us through the following methods", "customerService": {"title": "Customer Service", "phone": {"label": "Phone Consultation", "value": "************ (Weekdays 9:00-18:00)"}, "email": {"label": "Email Consultation", "value": "<EMAIL>"}, "online": {"label": "Online Support", "value": "Click the customer service icon in the bottom right corner for immediate consultation"}}, "inquiry": {"title": "Submit Inquiry", "form": {"name": {"label": "Your Name *", "placeholder": "Please enter your name", "required": "Please enter your name", "minLength": "Name must be at least 2 characters", "maxLength": "Name cannot exceed 30 characters"}, "email": {"label": "Email Address *", "placeholder": "We will contact you through this email", "required": "Please enter your email address", "invalid": "Please enter a valid email address", "maxLength": "Email cannot exceed 50 characters"}, "companyName": {"label": "Company Name", "placeholder": "Optional", "maxLength": "Company name cannot exceed 50 characters"}, "phone": {"label": "Contact Phone", "placeholder": "Optional, e.g.: ****** 456 7890", "invalid": "Please enter a valid phone number"}, "description": {"label": "Procurement Requirements *", "placeholder": "Please describe your procurement needs in detail, including product types, quantities, budget, etc.", "required": "Please describe your procurement needs", "minLength": "Description must be at least 10 characters", "maxLength": "Description cannot exceed 500 characters"}, "submit": "Submit Inquiry"}, "success": {"title": "Submission Successful!", "message": "Your inquiry has been successfully submitted. Our customer service will contact you within 24 hours", "button": "Return to Home"}, "notification": {"success": "Your inquiry has been successfully submitted. Our customer service will contact you soon", "error": "Submission failed, please try again later"}}}}, "__section_account_addresses": "==================== Shipping Addresses Page ====================", "addresses": {"title": "Shipping Addresses", "loading": "Loading...", "noAddresses": "No shipping addresses yet. Please click \"Add Address\" button to add one.", "labels": {"default": "<PERSON><PERSON><PERSON>"}, "buttons": {"addAddress": "Add Address", "setDefault": "Set as <PERSON><PERSON><PERSON>", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "save": "Save Address"}, "dialog": {"addTitle": "Add New Address", "editTitle": "Edit Address", "deleteConfirm": "Are you sure you want to delete this address?"}, "pagination": {"info": "Total {total} records, Page {current} / {pages}"}, "notifications": {"fetchFailed": "Failed to fetch address list", "addSuccess": "Address added successfully", "updateSuccess": "Address updated successfully", "saveError": "Failed to save address", "deleteSuccess": "Address deleted successfully", "deleteError": "Failed to delete address", "setDefaultSuccess": "Default address set successfully", "setDefaultError": "Failed to set default address"}, "form": {"sections": {"addressInfo": "Address Information", "recipientInfo": "Recipient Information", "regionInfo": "Region Information", "cityInfo": "City Information", "detailAddress": "Detailed Address"}, "fields": {"alias": "Address Alias", "aliasHint": "Optional, e.g.: Home, Office, etc.", "name": "Recipient Name", "mobile": "Mobile Number", "country": "Country/Region", "postCode": "Postal Code", "state": "State/Province", "city": "City", "detailAddress": "Detailed Address", "detailAddressHint": "Please enter detailed street, building number, etc.", "defaultAddress": "Set as default shipping address"}, "validations": {"nameRequired": "Please enter recipient name", "mobileRequired": "Please enter mobile number", "countryRequired": "Please select a country/region", "postCodeRequired": "Please enter postal code", "postCodeInvalid": "Please enter a valid postal code", "stateRequired": "Please select a state/province", "stateInputRequired": "Please enter state/province", "cityRequired": "Please select a city", "cityInputRequired": "Please enter city", "detailAddressRequired": "Please enter detailed address"}, "placeholders": {"selectCountry": "Please select a country/region"}}, "countries": {"china": "China", "usa": "USA", "japan": "Japan", "korea": "Korea", "uk": "UK", "germany": "Germany", "france": "France", "canada": "Canada", "australia": "Australia", "newZealand": "New Zealand", "singapore": "Singapore", "malaysia": "Malaysia", "thailand": "Thailand", "indonesia": "Indonesia", "philippines": "Philippines", "vietnam": "Vietnam", "russia": "Russia", "india": "India", "brazil": "Brazil", "mexico": "Mexico"}}, "__section_account_profile": "==================== Profile Page ====================", "profile": {"title": "Personal Information", "sections": {"basicInfo": "Basic Information", "contactInfo": "Contact Information", "security": "Security Settings"}, "fields": {"username": "Username", "nickname": "Nickname", "gender": "Gender", "avatar": "Avatar", "email": "Email", "mobile": "Mobile Number"}, "values": {"notSet": "Not Set", "userAvatar": "User Avatar", "avatarPreview": "Avatar Preview", "verified": "Verified", "unverified": "Unverified"}, "gender": {"secret": "Private", "male": "Male", "female": "Female"}, "countries": {"china": "China", "usa": "USA", "japan": "Japan", "korea": "Korea", "uk": "UK", "australia": "Australia", "singapore": "Singapore"}, "buttons": {"edit": "Edit", "save": "Save", "cancel": "Cancel", "modify": "Modify", "selectImage": "Select Image", "sendVerifyEmail": "Send Verification Email", "getCode": "Get Code", "resendAfter": "Resend in {seconds}s"}, "placeholders": {"username": "Please enter username", "nickname": "Please enter nickname", "mobile": "Please enter mobile number", "verificationCode": "Please enter verification code"}, "validations": {"usernameRequired": "Username is required", "mobileRequired": "Mobile number is required", "codeRequired": "Verification code is required"}, "tips": {"avatarFormat": "Supports JPG and PNG formats, file size should not exceed 2MB"}, "notifications": {"getUserInfoFailed": "Failed to get user information, please refresh the page", "imageSizeLimit": "Image size cannot exceed 2MB", "basicInfoUpdated": "Basic information updated successfully", "updateFailed": "Update failed, please try again", "saveFailed": "Save failed, please try again"}}, "__section_account_order_detail": "==================== Order Detail Page ====================", "accountOrderDetail": {"title": "Order Details", "backToList": "Back to Orders", "loading": "Loading order information...", "notFound": "Order not found or has been deleted", "sections": {"basicInfo": "Basic Information", "productInfo": "Product Information", "shippingInfo": "Shipping Information", "expressInfo": "Delivery Information"}, "fields": {"orderNumber": "Order #:", "createTime": "Created:", "orderStatus": "Status:", "paymentMethod": "Payment Method:", "paymentTime": "Payment Time:", "paymentOrder": "Payment Order:", "remarks": "Remarks:", "recipient": "Recipient:", "contactPhone": "Phone:", "shippingAddress": "Address:", "expressCompany": "Courier:", "trackingNumber": "Tracking #:", "expressRemarks": "Delivery Notes:"}, "table": {"productInfo": "Product", "unitPrice": "Price", "quantity": "Qty", "subtotal": "Subtotal"}, "summary": {"item": "Product", "price": "Price", "quantity": "Qty", "subtotal": "Subtotal", "unitPrice": "Price: ", "quantityLabel": "Qty: ", "subtotalLabel": "Subtotal: "}, "price": {"totalProducts": "Products Total:", "shipping": "Shipping:", "discount": "Discount:", "actualPayment": "Total Payment:", "serviceFee": "Service Fee:", "platformFee": "Platform Fee:"}, "buttons": {"cancelOrder": "Cancel Order", "payNow": "Pay Now", "confirmReceipt": "Confirm Receipt", "reviewProduct": "Write Review", "deleteOrder": "Delete Order", "trackShipment": "Track Shipment", "close": "Close", "print": "Print Order"}, "placeholders": {"unpaid": "Unpaid", "none": "None"}, "logistics": {"title": "Tracking Information", "loading": "Retrieving tracking information...", "trackTitle": "Tracking Details", "noInfo": "No tracking information available", "shipped": "Shipped", "latestStatus": "Latest Status"}, "confirmations": {"cancelTitle": "Cancel Order", "cancelMessage": "Are you sure you want to cancel this order?", "cancelSuccess": "Order cancelled successfully", "cancelFailed": "Failed to cancel order", "receiptTitle": "Confirm Receipt", "receiptMessage": "Confirm that you have received the products?", "receiptSuccess": "Receipt confirmed", "receiptFailed": "Failed to confirm receipt", "deleteTitle": "Delete Order", "deleteMessage": "Are you sure you want to delete this order? This cannot be undone!", "deleteSuccess": "Order deleted successfully", "deleteFailed": "Failed to delete order"}, "errors": {"fetchFailed": "Failed to fetch order details", "fetchError": "Error fetching order details", "trackFailed": "Failed to retrieve tracking information"}, "paymentMethods": {"wallet": "Wallet Balance", "wechat": "WeChat Pay", "alipay": "Alipay", "unpaid": "Unpaid"}, "status": {"unpaid": "Unpaid", "purchasing": "Purchasing", "shipping": "Shipping", "warehoused": "In Warehouse", "cancelled": "Cancelled", "additionalPayment": "Additional Payment", "completed": "Completed", "unknown": "Unknown Status"}, "progress": {"unpaid": "Unpaid", "purchasing": "Purchasing", "warehoused": "In Warehouse"}}, "__section_error": "==================== Error Messages ====================", "error": {"requiredAgreement": "You must agree to the terms and conditions!", "verifyCodeError": "Failed to send verification code.", "page": {"backToHome": "Back to Home", "goBack": "Go Back", "needHelp": "Need Help?", "contactSupport": "If the problem persists, please contact our customer service team:", "contactSupportError": "If you believe this is an error, please contact our customer service team:", "errorCode": "Error", "refreshPage": "Refresh Page", "search": "Search", "reLogin": "Login Again", "suggestions": "You might want to visit:", "homePage": "Home", "productList": "Products", "userCenter": "My Account", "helpCenter": "Help Center", "titles": {"400": "Bad Request", "401": "Unauthorized", "403": "Access Denied", "404": "Page Not Found", "500": "Server Error", "503": "Service Unavailable", "default": "An Error Occurred"}, "descriptions": {"400": "Sorry, your request contains invalid syntax and cannot be understood by the server.", "401": "Sorry, you need to log in to access this page.", "403": "Sorry, you don't have permission to access this page.", "404": "Sorry, the page you're looking for doesn't exist or has been removed.", "500": "Sorry, the server encountered an error. We're working to fix it.", "503": "Sorry, the service is temporarily unavailable. Please try again later.", "default": "Sorry, an unknown error occurred. Please try again later."}}}, "__section_products": "==================== Products Related ====================", "products": {"title": "Products", "categories": "Categories", "allCategories": "All Products", "showCategories": "Show Categories", "sortBy": "Sort By", "sortOptions": {"newest": "Newest", "priceAsc": "Price: Low to High", "priceDesc": "Price: High to Low", "popular": "Popularity"}, "noProducts": "No products found", "filter": "Filter", "gridView": "Grid View", "listView": "List View", "addToCart": "Add to Cart", "addToWishlist": "Add to Wishlist", "quickView": "Quick View", "outOfStock": "Out of Stock", "inStock": "In Stock", "freeShipping": "Free Shipping", "source": "Source", "shop": "Shop", "brand": "Brand", "price": "Price", "priceRange": "Price Range", "apply": "Apply", "reset": "Reset", "searchPlaceholder": "Search products", "totalItems": "{count} items total", "showingItems": "Showing {start}-{end} of {total} items", "detail": {"productDetail": "Product Details", "productLink": "Product Link", "price": "Price", "save": "Save", "cancel": "Cancel", "priceEditTip": "Price Edit Tip", "stock": "Stock", "freight": "Shipping", "quantity": "Quantity", "memo": "Memo", "addToCart": "Add to Cart", "buyNow": "Buy Now", "addToWishlist": "Add to Wishlist", "description": "Description", "purchaseNotes": "Purchase Notes", "afterSale": "After-Sale Service", "purchaseNotesContent": "Purchase Notes: Products will be shipped within 7 days after purchase. Please be patient.", "afterSaleContent": "After-Sale Service: 7-day return policy. Products must be unused and in perfect condition.", "selectSpec": "Please select specifications", "insufficientStock": "Insufficient stock", "addedToCart": "Added to cart", "addToCartFailed": "Failed to add item to the cart. Please try again later.", "addedToWishlist": "Added to wishlist"}}, "__section_verify": "==================== Email Verification ====================", "verify": {"loading": "Verifying your email...", "success": {"title": "Email Verified Successfully!", "greeting": "Congratulations", "message": "You have successfully registered on", "coupon": "A welcome coupon has been sent to your account:", "checkCoupon": "Click to check your coupons", "startShopping": "Begin Shopping Now", "redirect": "Redirecting to homepage in", "seconds": "seconds"}, "failed": {"title": "Verification Failed", "message": "The verification link may be invalid or expired. Please try again.", "invalidLink": "Invalid verification link.", "unexpectedError": "An unexpected error occurred.", "help": "You can try the following:", "checkEmail": "Check if you clicked the correct link from your email", "tryAgain": "Request a new verification email", "contactSupport": "Contact our support team if the problem persists", "resend": "Resend Verification Email", "goHome": "Go to Homepage"}}, "__section_cookies_consent": "==================== <PERSON>ie Consent ====================", "cookiesConsent": {"consent": {"title": "<PERSON><PERSON>", "message": "We use cookies to improve your browsing experience, personalize content and ads, provide social media features and analyze our traffic.", "learnMore": "Click to learn more about how we use", "cookiesLink": "cookies", "andText": "and", "privacyLink": "privacy policy", "details": ".", "customize": "Customize", "reject": "Reject", "accept": "Accept All"}, "customize": {"title": "<PERSON><PERSON>", "essential": "Essential Cookies", "essentialDesc": "These cookies are necessary for the website to function and cannot be switched off.", "performance": "Performance & Analytics Cookies", "performanceDesc": "These cookies help us understand how visitors interact with our website.", "functional": "Functional Cookies", "functionalDesc": "These cookies enable the website to remember your preferences and choices.", "targeting": "Targeting & Advertising Cookies", "targetingDesc": "These cookies are used to track your browsing habits to deliver relevant advertising.", "cancel": "Cancel", "save": "Save Settings"}}, "__section_text": "==================== Text Content ====================", "text": {"lastUpdated": "Last Updated", "loginPolicyText": "By logging in, you agree to our", "rememberPassword": "Remember your password?", "resetPasswordDescription": "Please enter your email address and we'll send you a verification code to reset your password."}, "__section_agreement": "==================== User Agreement ====================", "agreement": {"introduction": {"title": "Introduction", "content": "Welcome to LILISHOP services. This User Agreement (\"Agreement\") is a legal agreement between you and LILISHOP regarding your use of our services. By using our services, you agree to the terms of this Agreement. Please read it carefully."}, "services": {"title": "Service Description", "content": "LILISHOP is a leading global purchasing agent e-commerce platform, providing cross-border shopping, forwarding, and purchasing services. Our services may be updated continuously, and the form and nature of the services may change at any time without prior notice."}, "account": {"title": "User Account", "content1": "You need to create an account to use some of our services. You promise to provide accurate and complete registration information and update it promptly when changes occur.", "content2": "You are responsible for maintaining the security of your account, including protecting your password and limiting access to your computer. You agree to be responsible for all activities that occur under your account."}, "userConduct": {"title": "User Conduct", "content1": "You agree not to use our services for any illegal or unauthorized activities, including but not limited to:", "item1": "Violating any applicable laws and regulations", "item2": "Infringing on intellectual property rights or other rights of others", "item3": "Distributing spam, fraudulent information, or malware", "item4": "Interfering with or disrupting the normal operation of our services"}, "purchases": {"title": "Purchases and Payments", "content1": "When purchasing products through our platform, you agree to pay all applicable fees, including product prices, shipping fees, taxes, and service fees.", "content2": "We strive to ensure the accuracy of product information but are not responsible for issues caused by information errors. The final prices and terms are subject to the information at the time of order confirmation."}, "shipping": {"title": "Shipping and Forwarding", "content1": "We provide international forwarding services to help you ship products from the origin to your specified address.", "content2": "Delivery times are estimates only and may be affected by various factors, including but not limited to customs inspections, weather conditions, and logistics delays.", "content3": "You are responsible for ensuring that the shipping address provided is accurate and complete, and for complying with import regulations in the destination country/region."}, "returns": {"title": "Returns and Refunds", "content1": "Our return policy is limited by the original seller's policies and the special nature of international shipping.", "content2": "If you need to return an item, you must contact us within the specified time after receiving the item and provide necessary documentation.", "content3": "Refunds will be processed according to the original payment method and may require processing time."}, "liability": {"title": "Limitation of Liability", "content1": "To the maximum extent permitted by law, LILISHOP and its affiliates, employees, and agents are not liable for any direct, indirect, incidental, special, punitive, or consequential damages resulting from the use or inability to use our services.", "content2": "Our total liability will not exceed the amount you paid us for the relevant services in the 12 months preceding the relevant event."}, "disputes": {"title": "Dispute Resolution", "content1": "This Agreement is governed by the laws of the People's Republic of China.", "content2": "Any disputes arising from or related to this Agreement shall first be resolved through friendly negotiation. If negotiation fails, either party may file a lawsuit with a court of competent jurisdiction."}, "changes": {"title": "Agreement Changes", "content1": "We may modify this Agreement from time to time. The modified Agreement will be posted on our website and will be effective upon posting.", "content2": "Continued use of our services indicates your acceptance of the modified Agreement. If you do not agree with the modifications, please stop using our services."}, "contact": {"title": "Contact Us", "content": "If you have any questions about this Agreement, please contact us at:"}}, "__section_privacy": "==================== Privacy Policy ====================", "privacy": {"introduction": {"title": "Introduction", "content": "LILISHOP (\"we\", \"our\", or \"the Company\") respects and protects user privacy. This Privacy Policy explains how we collect, use, disclose, process, and protect the information you provide to us through our website, applications, and services. Please read this Privacy Policy carefully to understand our privacy practices."}, "collection": {"title": "Information Collection", "content1": "We may collect the following types of information:", "item1": {"title": "Personal Identification Information", "content": "Including but not limited to your name, email address, phone number, mailing address, and ID number (if applicable for customs declaration)."}, "item2": {"title": "Account Information", "content": "Information you provide when creating an account, such as username, password, security questions and answers."}, "item3": {"title": "Transaction Information", "content": "Your purchase history, payment methods, billing and shipping information."}, "item4": {"title": "Device Information", "content": "Device identifiers, IP addresses, browser types, operating system versions, language settings, etc."}, "item5": {"title": "Usage Data", "content": "Information about how you use our services, such as browsing history, search queries, click behavior, etc."}, "item6": {"title": "Cookies and Similar Technologies", "content": "We use cookies and similar technologies to collect information. See our Cookie Policy for details."}}, "use": {"title": "Information Use", "content1": "We may use the collected information for the following purposes:", "item1": "Providing, maintaining, and improving our services", "item2": "Processing and completing your transactions", "item3": "Managing your account, including authentication", "item4": "Providing customer support and responding to your requests", "item5": "Sending service notifications and updates", "item6": "Sending marketing and promotional information (if you consent to receive it)", "item7": "Preventing fraud and enhancing security", "item8": "Conducting data analysis and research to improve our services", "item9": "Complying with legal obligations"}, "sharing": {"title": "Information Sharing", "content1": "We may share your information in the following circumstances:", "item1": {"title": "Service Providers", "content": "With third-party service providers who help us provide services, such as payment processors, logistics companies, customer service providers, etc."}, "item2": {"title": "Business Partners", "content": "With our business partners to provide services you request or complete transactions you authorize."}, "item3": {"title": "Legal Requirements", "content": "When we believe in good faith that disclosure is required by law, such as in response to a subpoena, court order, or other legal process."}, "item4": {"title": "Protection of Rights", "content": "When we believe in good faith that disclosure is necessary to protect our rights, property, or safety, or the rights, property, or safety of our users or the public."}, "item5": {"title": "Business Transfers", "content": "In the event of a merger, acquisition, asset sale, or similar transaction, your information may be transferred as part of the transferred assets."}, "content2": "We do not sell your personal information."}, "security": {"title": "Data Security", "content1": "We implement appropriate technical and organizational measures to protect your personal information from accidental or unlawful destruction, loss, alteration, unauthorized disclosure, or access.", "content2": "However, no Internet transmission or electronic storage method is 100% secure. Therefore, while we strive to use commercially acceptable means to protect your personal information, we cannot guarantee its absolute security."}, "retention": {"title": "Data Retention", "content1": "We will retain your personal information for as long as necessary to fulfill the purposes outlined in this Privacy Policy, unless a longer retention period is required or permitted by law.", "content2": "When we no longer need to use your personal information, we will delete it from our systems or anonymize it so that it can no longer identify you."}, "rights": {"title": "Your Rights", "content1": "Depending on applicable laws in your region, you may have the following rights:", "item1": "Access your personal information", "item2": "Correct inaccurate or incomplete personal information", "item3": "Delete your personal information", "item4": "Restrict or object to the processing of your personal information", "item5": "Data portability", "item6": "Withdraw consent", "content2": "To exercise these rights, please contact us using the contact information provided below."}, "children": {"title": "Children's Privacy", "content1": "Our services are not directed to children under 13 years of age. We do not knowingly collect personal information from children under 13. If you are a parent or guardian and believe your child has provided us with personal information, please contact us, and we will take steps to delete such information."}, "international": {"title": "International Data Transfers", "content1": "Your personal information may be transferred to and stored in countries outside your country of residence, where data protection laws may differ from those in your country.", "content2": "When we transfer your personal information to other countries, we will take appropriate measures to ensure your personal information receives adequate protection and that the transfer complies with applicable data protection laws."}, "cookies": {"title": "<PERSON><PERSON>", "content1": "Cookies are small text files placed on your device to record information about your visit to our website. We use cookies and similar technologies to:", "item1": "Make our website function properly", "item2": "Remember your preferences", "item3": "Understand how you use our website", "item4": "Personalize your experience", "item5": "Provide relevant advertising", "content2": "You can control and delete cookies through your browser settings. However, if you choose to disable cookies, you may not be able to use some features of our website.", "content3": "For more information about how we use cookies, please see our Cookie Policy."}, "changes": {"title": "Privacy Policy Changes", "content1": "We may update this Privacy Policy from time to time. When we make significant changes, we will post the updated policy on our website and notify you when appropriate.", "content2": "We encourage you to review this Privacy Policy periodically to stay informed about how we protect your information."}, "contact": {"title": "Contact Us", "content1": "If you have any questions, comments, or requests regarding this Privacy Policy, please contact us at:", "content2": "We will respond to your request within a reasonable time."}}, "__section_cookie_policy": "==================== Cookie Policy ====================", "cookiePolicy": {"introduction": {"title": "What Are Cookies?", "content1": "Cookies are small text files placed on your computer or mobile device when you visit a website, allowing the website to \"remember\" your actions and preferences. Cookies typically contain the name of the website that issued the cookie, the \"lifetime\" of the cookie (i.e., how long it will remain on your device), and a value, usually a randomly generated unique number.", "content2": "In addition to cookies, we may also use web beacons, pixel tags, and other similar technologies. These are small graphic files embedded in web pages or emails, used to collect information about how you use our services. In this policy, we collectively refer to these technologies as \"cookies\"."}, "types": {"title": "Types of Cookies We Use", "content": "We use the following types of cookies:", "essential": {"title": "Essential Cookies", "content": "These cookies are essential for providing the services you request through our website, enabling you to use certain features such as accessing secure areas. Without these cookies, we cannot provide some services you request."}, "performance": {"title": "Performance and Analytics Cookies", "content": "These cookies collect information about how you use our website, such as which pages you visit and any errors you encounter. These cookies do not collect information that identifies you; all information is anonymous. We use this information to improve our website, measure the effectiveness of our ads and web content, and develop new features."}, "functional": {"title": "Functional Cookies", "content": "These cookies allow the website to remember choices you make (such as your username, language, or region) and provide enhanced, more personalized features. These cookies can also be used to remember changes you make to text size, fonts, and other customizable parts of web pages. They may also be used to provide services you request, such as watching a video or commenting on social media."}, "targeting": {"title": "Targeting and Advertising Cookies", "content": "These cookies are used to deliver advertisements more relevant to you and your interests. They are also used to limit the number of times you see an advertisement and help measure the effectiveness of advertising campaigns. They are usually placed by advertising networks with the website operator's permission. They remember that you have visited a website, and this information may be shared with other organizations such as advertisers."}, "thirdParty": {"title": "Third-Party Cookies", "content": "Our website has some cookies from third parties, such as analytics partners or advertising networks. These third parties may collect information about your online activities to help them provide relevant advertising or analyze the use of our website."}}, "purposes": {"title": "Specific Purposes of Cookies", "content": "The specific purposes for which we use cookies include:", "item1": "Verifying user identity and ensuring account security", "item2": "Remembering user login status so users don't have to log in again each time they visit the website", "item3": "Remembering user language and other preferences", "item4": "Analyzing how users use our website so we can improve its structure and content", "item5": "Collecting statistics such as visitor numbers and usage", "item6": "Customizing content and advertisements based on user interests and behavior", "item7": "Preventing fraudulent activities and improving website security"}, "control": {"title": "How to Control Cookies", "content1": "Most web browsers automatically accept cookies, but if you prefer, you can usually modify your browser settings to decline cookies. Here is information on how to manage cookies in different browsers:", "content2": "Please note that if you choose to decline cookies, you may not be able to use some features of our website. Additionally, you can learn more about managing and deleting cookies by visiting the following websites:"}, "doNotTrack": {"title": "Do Not Track Signals", "content": "Some browsers include a \"Do Not Track\" (DNT) feature that can send a signal to websites indicating that you do not wish to be tracked. Because there is not yet a common understanding of how to interpret DNT signals, we currently do not respond to browser DNT requests."}, "updates": {"title": "Updates to This Policy", "content": "We may update this Cookie Policy from time to time to reflect changes in our use of cookies or for other operational, legal, or regulatory reasons. Please review this policy periodically to stay informed about how we use cookies."}, "contact": {"title": "Contact Us", "content": "If you have any questions about our use of cookies, please contact us at:"}, "consent": {"title": "<PERSON><PERSON>", "content1": "When you first visit our website, we will notify you about our use of cookies through a cookie banner. By continuing to use our website, you consent to our use of cookies in accordance with this policy.", "content2": "If you do not wish to accept cookies, please adjust your browser settings as described in the \"How to Control Cookies\" section above."}}, "__section_nav_menu": "==================== Navigation Menu ====================", "nav": {"home": "Home", "products": "Products", "categories": "Categories", "search": "Search Results", "cart": "<PERSON><PERSON>", "checkout": "Checkout", "account": "My Account"}, "__section_transfer": "==================== Transfer Orders ====================", "transfer": {"title": "International Shipping Service", "subtitle": "Fast, Secure, and Convenient International Logistics Solutions", "description": "We provide professional international shipping services to help you easily transport goods from China to anywhere in the world.", "steps": {"title": "Shipping Process", "step1": {"title": "Create Shipping Order", "description": "Log in to your account, create a shipping order in your dashboard"}, "step2": {"title": "Send Package", "description": "Send your package to our warehouse in China"}, "step3": {"title": "Warehouse Receipt", "description": "We receive and verify your package, then update its status"}, "step4": {"title": "Pay Shipping Fee", "description": "Confirm package details and pay international shipping fee"}, "step5": {"title": "International Shipping", "description": "We arrange international shipping for your package"}, "step6": {"title": "Receive Package", "description": "Package arrives at destination, you receive it"}}, "features": {"title": "Service Features", "feature1": {"title": "Global Delivery", "description": "Coverage in over 200 countries and regions"}, "feature2": {"title": "Professional Packaging", "description": "Professional packing to ensure safe transport"}, "feature3": {"title": "Real-time Tracking", "description": "Real-time logistics status updates"}, "feature4": {"title": "Multiple Shipping Options", "description": "Various shipping methods to meet different needs"}, "feature5": {"title": "Storage Service", "description": "Free storage service, option to combine packages"}, "feature6": {"title": "Professional Support", "description": "Multilingual customer service team"}}, "faq": {"title": "Frequently Asked Questions", "question1": "How do I create a shipping order?", "answer1": "Log in to your account, go to your dashboard, click on 'My Shipping Orders', then click the 'Create Shipping Order' button and fill in the required information.", "question2": "What is the address for shipping packages?", "answer2": "After creating a shipping order, the system will generate a warehouse address specific to you. You need to provide this address to the seller or shipping company.", "question3": "What shipping methods do you support?", "answer3": "We offer various international shipping methods, including standard air shipping, express air shipping, sea shipping, etc. You can choose the appropriate method based on your needs.", "question4": "How is shipping fee calculated?", "answer4": "Shipping fees are calculated based on the weight, volume, and destination country of your package. You can estimate fees using our shipping calculator.", "question5": "How long can packages be stored?", "answer5": "We provide 30 days of free storage. Storage fees will apply after 30 days.", "question6": "Can I combine multiple packages?", "answer6": "Yes, you can wait for multiple packages to arrive at our warehouse and choose to ship them together to save on shipping costs."}, "cta": {"title": "Start Using Our Shipping Service", "subtitle": "Just a few simple steps to enjoy professional international shipping service", "button": "Create Shipping Order", "loginFirst": "Login to Create Order"}, "page": {"title": "China Warehouse Address", "name": "Recipient: {name}", "address": "Address: {address}", "phone": "Phone: {phone}", "note": "Note: Please include your shipping order number on the package"}, "common": {"myTransfer": "My Transfer Orders", "createTransfer": "Create Transfer Order", "editTransfer": "Edit Transfer Order", "transferDetail": "Transfer Order Details", "backToList": "Back to List", "backToDetail": "Back to Details", "loading": "Loading...", "noData": "No transfer order found", "noDataOrCannotEdit": "Transfer order not found or cannot be edited", "returnToList": "Return to Transfer List", "transferNumber": "Transfer Order #", "createTime": "Created Time", "expressCompany": "Courier", "expressNumber": "Tracking Number", "itemCount": "<PERSON><PERSON>", "status": "Status", "actions": "Actions", "viewDetail": "View Details", "edit": "Edit", "cancel": "Cancel", "delete": "Delete", "save": "Save Changes", "copySuccess": "Copied to clipboard", "copyFailed": "Co<PERSON> failed", "piece": "pcs", "unknown": "Unknown", "kg": "kg", "noRemark": "None", "copyNumber": "Copy Number"}, "list": {"warehouseAddress": "My Warehouse Address", "searchPlaceholder": "Enter transfer order number", "noTransferRecords": "No transfer orders found", "itemList": "Item List", "category": "Category", "name": "Name", "quantity": "Quantity", "weight": "Weight", "totalRecords": "Total {total} records, Page {current} / {totalPages}"}, "status": {"pending": "Pending", "warehoused": "In Warehouse", "toBeShipped": "To Be Shipped", "shipped": "Shipped", "completed": "Completed", "cancelled": "Cancelled", "unknown": "Unknown Status", "all": "All", "inWarehouse": "In Warehouse", "readyToShip": "Ready to Ship"}, "create": {"selectWarehouse": "Select Destination Warehouse", "warehouseNote": "Warehouse only supports packages not exceeding your mainland address and overseas (including Hong Kong, Macao and Taiwan) current residence address", "selectWarehouseLabel": "Select Warehouse", "warehouseRequired": "Please select a warehouse", "expressInfo": "Enter Domestic Express Information", "expressNote": "The tracking number is the express number you send to our warehouse, please ensure the information is accurate", "expressCompanyLabel": "Express Company", "expressCompanyRequired": "Please select an express company", "expressNumberLabel": "Tracking Number", "expressNumberRequired": "Please enter tracking number", "otherExpressLabel": "Please specify express company name", "otherExpressRequired": "Please specify express company name", "productInfo": "Enter Product Information for Forwarding", "productNote": "Due to customs requirements, please fill in product information truthfully, including category, name, quantity, weight, etc.", "product": "Product", "deleteProduct": "Delete this product", "categoryLabel": "Product Category", "categoryRequired": "Please select product category", "productNameLabel": "Product Name", "productNameRequired": "Please enter product name", "quantityLabel": "Quantity", "quantityRequired": "Please enter quantity", "quantityPositive": "Quantity must be greater than 0", "weightLabel": "Weight (kg)", "weightPositive": "Weight must be greater than 0", "priceLabel": "Value (CNY)", "addProduct": "Add Product", "remarkInfo": "Remarks", "remarkNote": "If you have special requirements or need to explain something, please fill in here", "remarkPlaceholder": "Please enter remarks for the transfer order (optional)", "remarkMaxLength": "Remarks cannot exceed 300 characters", "notice": "Important Notes", "noticeItems": ["Please ensure the information you fill in is accurate, especially the tracking number and product information.", "Forwarding service only supports legal items, prohibited items and infringing products are not allowed.", "After the package is received in our warehouse, we will notify you and provide actual weight and volume information.", "Please submit a shipping request promptly after receiving the warehouse notification. If no shipping request is submitted within one month, the platform will charge storage fees."], "agreement": "I have read and agree to the \"Disclaimer\" and \"Forwarding Rules and Service Terms\"", "agreementRequired": "Please read and agree to the terms", "submit": "Submit Transfer Order", "otherExpressNote": "Please specify express company name", "createSuccess": "Transfer order created successfully", "createFailed": "Failed to create transfer order"}, "edit": {"expressInfoUnchangeable": "Express Information (Cannot be modified)", "expressInfoNote": "Express information is core to the transfer order and cannot be modified after submission", "warehouseInfoUnchangeable": "Warehouse Information (Cannot be modified)", "warehouseInfoNote": "Destination warehouse information cannot be modified after submission", "targetWarehouse": "Destination Warehouse", "productInfoChangeable": "Product Information (Can be modified)", "productInfoNote": "You can modify product information, including category, name, quantity, weight, etc.", "remarkChangeable": "Remarks (Can be modified)", "editNotice": "Editing Instructions", "editNoticeItems": ["You can only modify product information and remarks. Express and warehouse information cannot be modified.", "Once the transfer order is received in warehouse (status changes to \"In Warehouse\"), no information can be modified.", "Please ensure the modified information is accurate, especially product information.", "If you need to modify express information, please cancel the current transfer order and create a new one."], "updateSuccess": "Transfer order updated successfully", "updateFailed": "Failed to update transfer order"}, "detail": {"basicInfo": "Basic Information", "targetWarehouse": "Destination Warehouse", "remarkInfo": "Remarks", "itemList": "Item List", "expressInfo": "Express Information", "expressRemark": "Express Remarks", "trackExpress": "Track Shipment", "applyForShipment": "Apply for Shipping", "applyConfirmTitle": "Apply for Shipping", "applyConfirmMessage": "Are you sure you want to apply for shipping?", "applySuccess": "Shipping application submitted successfully, please wait for processing", "deleteConfirmTitle": "Confirm Deletion", "deleteConfirmMessage": "Are you sure you want to delete this transfer order? This action cannot be undone!", "deleteSuccess": "Transfer order deleted", "deleteFailed": "Failed to delete transfer order", "cancelConfirmTitle": "Confirm Cancellation", "cancelConfirmMessage": "Are you sure you want to cancel this transfer order?", "cancelSuccess": "Transfer order cancelled", "cancelFailed": "Failed to cancel transfer order", "onlyPendingCanEdit": "Only pending transfer orders can be edited", "onlyCancelledCanDelete": "Only cancelled transfer orders can be deleted", "itemDetails": "Click to view details", "print": "Print Transfer Order", "pieces": "pcs", "expressNumberNotExist": "Express number does not exist", "confirmApplyForShipment": "Are you sure you want to apply for shipping?", "applyForShipmentSuccess": "Shipping application submitted successfully", "confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this transfer order? This action cannot be undone!"}, "warehouse": {"warehouseAddress": "My Warehouse Address", "addressInfo": "Warehouse Address Information", "selectAddress": "Please select the following address as your express delivery address:", "receiver": "Recipient", "phone": "Contact Phone", "address": "Detailed Address", "copyAddress": "Copy Address", "addressNote": "Please ensure that the recipient address on your express package is exactly the same as the above address, otherwise the package may not be correctly received in the warehouse.", "myWarehouseAddress": "My Warehouse Address"}, "errors": {"getListFailed": "Failed to get transfer order list", "getListError": "Error getting transfer order list", "cancelError": "Error cancelling transfer order", "deleteError": "Error deleting transfer order", "createError": "Error creating transfer order", "updateError": "Error updating transfer order", "getDetailError": "Error getting data"}}, "__section_warehouse": "==================== Warehouse Management ====================", "warehouse": {"myWarehouse": "My Warehouse", "searchPlaceholder": "Enter product name", "loading": "Loading data...", "noStockItems": "No inventory items", "quantity": "Quantity", "weight": "Weight", "volume": "Volume", "expiry": "Expiry", "totalItemWeight": "Total Item Weight", "estimatedPackagingWeight": "Estimated Packaging Weight", "estimatedParcelWeight": "Estimated <PERSON><PERSON><PERSON>", "selectAll": "Select All", "balance": "Balance", "submitParcel": "Submit <PERSON><PERSON><PERSON>", "weightInfo": "Weight Information", "totalRecords": "Total {total} records, Page {current} / {pages}", "productName": "Product Name", "productImage": "Product Image", "image": "Image", "productDetails": "Product Details", "status": "Status", "inboundDate": "Inbound Date", "expiryDate": "Expiry Date", "actions": "Actions", "viewDetails": "View Details", "title": "My Warehouse", "search": "Search", "createParcel": "Create <PERSON><PERSON><PERSON>", "selected": "Selected", "items": "items", "noItems": "No inventory items", "pagination": "Total {total} records, Page {current} / {totalPages}", "detail": "Product Details", "count": "Quantity", "inTime": "Inbound Time", "errors": {"fetchFailed": "Failed to fetch inventory list", "fetchError": "Error fetching inventory list"}}, "__section_warehouse_detail": "==================== Warehouse Detail Page ====================", "warehouseDetail": {"title": "Inventory Details", "backToList": "Back to List", "loading": "Loading...", "notFound": "Product information not found", "sections": {"basicInfo": "Basic Information", "detailedInfo": "Detailed Information", "properties": "Product Properties", "productImage": "Product Image", "inspectionImages": "Inspection Images", "inspectionVideos": "Inspection Videos", "relatedFiles": "Related Files"}, "fields": {"id": "ID", "spuName": "Product Name", "count": "Quantity", "weight": "Weight", "volume": "Volume", "status": "Status", "inTime": "Inbound Time", "expiredTime": "Expiry Time", "createTime": "Create Time"}, "status": {"normal": "Normal", "locked": "Locked", "expired": "Expired", "unknown": "Unknown Status"}, "placeholders": {"none": "None", "file": "File"}, "buttons": {"createParcel": "Create <PERSON><PERSON><PERSON>", "download": "Download"}, "imageViewer": {"title": "Image Viewer"}, "videoPlayer": {"title": "Video Player", "clickToPlay": "Click to play video"}, "errors": {"fetchFailed": "Failed to fetch inventory details", "fetchError": "Error fetching inventory details"}}, "__section_payment": "==================== Payment Page ====================", "payment": {"title": "Checkout", "orderStatus": {"submitted": "Order submitted successfully, please complete payment soon", "paid": "This order has been paid", "expired": "Order has expired", "notFound": "Payment order information not found", "remainingTime": "Payment time remaining: {h}:{m}:{s}"}, "amount": "Amount:", "payButton": "Pay Now", "insufficientBalance": "Insufficient balance, please select another payment method or recharge", "result": {"title": "Payment Result", "success": "Payment Successful", "failed": "Payment Failed", "closed": "Order Closed", "waiting": "Checking payment result...", "unknown": "Unknown Status", "backToHome": "Back to Home", "viewOrder": "View Order", "repay": "Pay Again"}, "paymentDialog": {"processing": "Payment Processing", "usingChannel": "You are paying with {channel}", "unknownChannel": "Unknown Payment Method", "message": "Please complete payment in the newly opened window, then click the button below", "completed": "Payment Completed", "reselect": "Choose Another Payment Method", "abandon": "Cancel Payment", "problem": "Payment Issues?", "helpTitle": "Payment Help", "defaultHelpMessage": "If you encounter any issues during payment, please contact customer service <NAME_EMAIL>", "simpleCompleted": "Completed", "simpleReselect": "Change", "simpleAbandon": "Cancel"}, "navigation": {"backToHome": "Back to Home", "viewOrder": "View Orders"}, "notification": {"selectPayment": "Please select a payment method", "paymentFailed": "Payment request failed", "paymentError": "Payment request error", "popupBlocked": "Unable to open payment page, please check your browser popup settings"}, "walletPay": {"title": "Wallet Payment", "confirmMessage": "You will pay with your account balance", "currentBalance": "Current account balance", "confirm": "Confirm Payment"}, "selector": {"title": "Select Payment Method", "fee": "Fee:", "feeTooltip": "Includes rate and fixed fee", "orderAmount": "Order Amount:", "actualPayment": "Total Payment:", "feeDescription": "Fee is", "feeDescriptionApprox": "approx.", "paymentMethods": {"paypal": {"title": "<PERSON><PERSON>", "description": "Accepts various foreign currencies, supports credit and debit cards"}, "stripe": {"title": "Stripe", "description": "Accepts various foreign currencies, supports credit and debit cards"}, "wechat": {"title": "WeChat Pay", "description": "Pay by scanning WeChat QR code"}, "alipay": {"title": "Alipay", "description": "Pay by scanning Alipay QR code"}, "wallet": {"title": "Wallet Balance", "description": "Pay with your account balance, current balance:"}}}}, "__section_parcel": "==================== Parcel Page ====================", "parcel": {"title": "My Parcels", "detail": {"title": "<PERSON><PERSON><PERSON>", "backToList": "Back to <PERSON><PERSON>el List", "loading": "Loading parcel information...", "notFound": "Parcel information not found", "sections": {"status": "Parcel Status", "parcelNumber": "Pa<PERSON>el <PERSON>", "createTime": "Created Time", "trackingNumber": "Tracking Number", "shippingMethod": "Shipping Method", "tracking": "Tracking Information", "items": "<PERSON><PERSON><PERSON>", "address": "Shipping Address", "customs": "Customs Information", "logistics": "Logistics Information", "services": "Service Information", "costs": "Cost Information", "remarks": "Parcel <PERSON>"}, "address": {"recipient": "Recipient", "phone": "Phone", "address": "Address"}, "customs": {"declaredValue": "Declared Value", "clearanceCode": "Clearance Code", "description": "Description", "none": "None"}, "logistics": {"estimatedDelivery": "Estimated Delivery", "weight": "<PERSON><PERSON><PERSON>", "noInfo": "No information", "notSelected": "Not selected"}, "services": {"insurance": "Insurance", "valueAdded": "Value-Added Services", "free": "Free Services", "notPurchased": "Not purchased", "notSelected": "Not selected"}, "costs": {"baseShipping": "Base Shipping Fee", "insurance": "Insurance Fee", "extraServices": "Extra Services", "couponDiscount": "Coupon Discount", "pointsDiscount": "Points Discount", "total": "Total"}, "itemInfo": {"weight": "Weight", "quantity": "Quantity"}, "copy": {"success": "Copied to clipboard", "failed": "Co<PERSON> failed"}}, "tabs": {"unpaid": "Unpaid", "unshipped": "Unshipped", "shipping": "Shipping", "completed": "Completed", "all": "All"}, "search": {"placeholder": "Enter parcel number"}, "table": {"details": "<PERSON><PERSON><PERSON>", "weight": "Weight", "quantity": "Quantity", "amount": "Amount", "status": "Status", "actions": "Actions"}, "parcelInfo": {"number": "Parcel No.", "totalAmount": "Total Amount", "weightUnit": "g", "itemCount": "Total {count} items, Amount"}, "status": {"unpaid": "Unpaid", "unshipped": "Unshipped", "shipping": "Shipping", "completed": "Completed", "cancelled": "Cancelled", "unknown": "Unknown"}, "actions": {"details": "Details", "cancel": "Cancel", "pay": "Pay Now", "track": "Track"}, "dialog": {"cancel": {"title": "Cancel <PERSON>", "message": "Are you sure you want to cancel this parcel?", "success": "Pa<PERSON>el cancelled"}, "delete": {"title": "Delete Parcel", "message": "Are you sure you want to delete this parcel? This action cannot be undone.", "success": "<PERSON><PERSON><PERSON> deleted"}, "track": {"title": "Tracking Information", "trackingNumber": "Tracking Number", "shippingMethod": "Shipping Method", "weight": "<PERSON><PERSON><PERSON>", "note": "For detailed tracking information, please visit the carrier's website"}}, "errors": {"emptyId": "Parcel ID cannot be empty", "fetchFailed": "Failed to fetch parcel list", "noTracking": "No tracking information available", "cancelFailed": "Failed to cancel parcel", "cancelError": "Error cancelling parcel", "deleteFailed": "Failed to delete parcel", "deleteError": "Error deleting parcel"}, "pagination": "Total {total} records, Page {current} / {pages}", "noRecords": "No parcel records found"}, "__section_wishlist": "==================== Wishlist Page ====================", "wishlist": {"title": "My Wishlist", "loading": "Loading...", "noProducts": "No saved items", "browseProducts": "Browse Products", "viewDetails": "View Details", "hot": "Hot", "confirmRemove": {"title": "Remove this item?", "cancel": "Cancel", "confirm": "Confirm"}, "removeSuccess": "Item removed from wishlist", "pagination": "Total {total} items, Page {current}/{pages}"}, "__section_message": "==================== Message Center ====================", "messageCenter": {"inbox": {"pageTitle": "Inbox", "messageType": "Message Type", "readStatus": "Read Status", "searchPlaceholder": "Search messages", "markAllRead": "<PERSON>", "batchDelete": "<PERSON><PERSON> Delete", "noMessages": "No messages", "totalRecords": "Total {total} records, Page {current}/{pages}", "typeColumn": "Type", "titleColumn": "Title", "contentColumn": "Content", "timeColumn": "Time", "actionsColumn": "Actions", "confirmDelete": "Confirm Delete", "confirmDeleteSingle": "Are you sure you want to delete this message?", "confirmDeleteMultiple": "Are you sure you want to delete {count} selected messages?", "cancel": "Cancel", "delete": "Delete", "deleteSuccess": "Deleted successfully", "deleteFailed": "Delete failed, please try again later", "markAllReadSuccess": "All messages marked as read", "markAllReadFailed": "Operation failed, please try again later", "loadFailed": "Failed to load messages, please try again later", "read": "Read", "unread": "Unread", "allMessages": "All Messages", "systemNotice": "System Notice", "orderNotice": "Order Notice", "transferNotice": "Transfer Notice", "accountNotice": "Account Notice", "activityNotice": "Activity Notice", "viewDetails": "View Details", "copyNumber": "Copy Number"}, "consult": {"pageTitle": "My Consultations", "consultType": "Consultation Type", "statusFilter": "Status", "dateRange": "Date Range", "searchPlaceholder": "Search consultations", "newConsult": "New Consultation", "noConsults": "No consultations", "idColumn": "ID", "typeColumn": "Type", "titleColumn": "Title", "createTimeColumn": "Created Time", "lastReplyTimeColumn": "Last Reply", "statusColumn": "Status", "actionsColumn": "Actions", "view": "View", "close": "Close", "confirmClose": "Confirm Close", "confirmCloseMessage": "Are you sure you want to close this consultation? You won't be able to reply after closing.", "cancel": "Cancel", "confirm": "Confirm", "closeSuccess": "Consultation closed", "closeFailed": "Operation failed, please try again later", "loadFailed": "Failed to load consultations, please try again later", "newConsultTitle": "New Consultation", "consultTypeRequired": "Consultation Type *", "relatedOrder": "Related Order", "relatedTransfer": "Related Transfer", "consultTitleRequired": "Consultation Title *", "consultContentRequired": "Consultation Content *", "attachments": "Attachments", "attachmentsHint": "Maximum 3 files, 5MB each", "submit": "Submit", "submitSuccess": "Consultation submitted successfully", "submitFailed": "Submission failed, please try again later", "fileRejected": "File does not meet requirements", "filesRejected": "{count} files do not meet requirements", "titleRule": "Title cannot be empty and must not exceed 50 characters", "contentRule": "Content cannot be empty and must not exceed 500 characters", "typeRule": "Please select a consultation type", "allTypes": "All Types", "orderConsult": "Order Consultation", "transferConsult": "Transfer Consultation", "productConsult": "Product Consultation", "aftersaleService": "After-sales Service", "complaint": "Complaint & Suggestion", "otherIssue": "Other Issues", "allStatuses": "All Statuses", "pending": "Pending Reply", "replied": "Replied", "closed": "Closed"}}}