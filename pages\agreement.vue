<template>
  <HeaderSimple />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />
  
  <div class="agreement-page">
    <div class="agreement-container">
      <div class="agreement-header">
        <h1 class="agreement-title">{{ $t('title.userAgreement') || '用户协议' }}</h1>
        <p class="agreement-date">{{ $t('text.lastUpdated') || '最后更新日期' }}: {{ lastUpdated }}</p>
      </div>
      
      <div class="agreement-content">
        <section class="agreement-section">
          <h2>1. {{ $t('agreement.introduction.title') || '引言' }}</h2>
          <p>{{ $t('agreement.introduction.content') || '欢迎使用LILISHOP服务。本用户协议（"协议"）是您与LILISHOP之间就使用我们的服务所达成的法律协议。使用我们的服务，即表示您同意本协议的条款。请仔细阅读。' }}</p>
        </section>
        
        <section class="agreement-section">
          <h2>2. {{ $t('agreement.services.title') || '服务说明' }}</h2>
          <p>{{ $t('agreement.services.content') || 'LILISHOP是一个全球领先的代购电商服务平台，为用户提供跨境购物、转运、代购等服务。我们的服务可能会不断更新，服务的形式和性质可能会随时变化，恕不另行通知。' }}</p>
        </section>
        
        <section class="agreement-section">
          <h2>3. {{ $t('agreement.account.title') || '用户账户' }}</h2>
          <p>{{ $t('agreement.account.content1') || '您需要创建账户才能使用我们的某些服务。您承诺提供准确、完整的注册信息，并在信息变更时及时更新。' }}</p>
          <p>{{ $t('agreement.account.content2') || '您有责任保护您的账户安全，包括保护您的密码和限制对您计算机的访问。您同意对您账户下发生的所有活动负责。' }}</p>
        </section>
        
        <section class="agreement-section">
          <h2>4. {{ $t('agreement.userConduct.title') || '用户行为' }}</h2>
          <p>{{ $t('agreement.userConduct.content1') || '您同意不会使用我们的服务进行任何违法或未经授权的活动，包括但不限于：' }}</p>
          <ul>
            <li>{{ $t('agreement.userConduct.item1') || '违反任何适用的法律法规' }}</li>
            <li>{{ $t('agreement.userConduct.item2') || '侵犯他人的知识产权或其他权利' }}</li>
            <li>{{ $t('agreement.userConduct.item3') || '传播垃圾信息、欺诈信息或恶意软件' }}</li>
            <li>{{ $t('agreement.userConduct.item4') || '干扰或破坏我们服务的正常运行' }}</li>
          </ul>
        </section>
        
        <section class="agreement-section">
          <h2>5. {{ $t('agreement.purchases.title') || '购买与支付' }}</h2>
          <p>{{ $t('agreement.purchases.content1') || '通过我们的平台购买商品时，您同意支付所有适用的费用，包括商品价格、运费、税费和服务费。' }}</p>
          <p>{{ $t('agreement.purchases.content2') || '我们会尽力确保商品信息的准确性，但不对因信息错误导致的问题承担责任。最终的价格和条款以订单确认时的信息为准。' }}</p>
        </section>
        
        <section class="agreement-section">
          <h2>6. {{ $t('agreement.shipping.title') || '配送与转运' }}</h2>
          <p>{{ $t('agreement.shipping.content1') || '我们提供国际转运服务，帮助您将商品从原产地运送到您指定的地址。' }}</p>
          <p>{{ $t('agreement.shipping.content2') || '配送时间仅为估计，可能受到多种因素影响，包括但不限于海关检查、天气条件和物流延误。' }}</p>
          <p>{{ $t('agreement.shipping.content3') || '您有责任确保提供的收货地址准确完整，并遵守目的地国家/地区的进口法规。' }}</p>
        </section>
        
        <section class="agreement-section">
          <h2>7. {{ $t('agreement.returns.title') || '退货与退款' }}</h2>
          <p>{{ $t('agreement.returns.content1') || '我们的退货政策受限于原始卖家的政策以及国际运输的特殊性。' }}</p>
          <p>{{ $t('agreement.returns.content2') || '如需退货，您必须在收到商品后的规定时间内联系我们，并提供必要的证明文件。' }}</p>
          <p>{{ $t('agreement.returns.content3') || '退款将按照原始支付方式处理，可能需要一定的处理时间。' }}</p>
        </section>
        
        <section class="agreement-section">
          <h2>8. {{ $t('agreement.liability.title') || '责任限制' }}</h2>
          <p>{{ $t('agreement.liability.content1') || '在法律允许的最大范围内，LILISHOP及其关联公司、员工、代理人不对因使用或无法使用我们的服务而导致的任何直接、间接、附带、特殊、惩罚性或后果性损害承担责任。' }}</p>
          <p>{{ $t('agreement.liability.content2') || '我们的责任总额不会超过您在相关事件发生前12个月内为使用相关服务而支付给我们的金额。' }}</p>
        </section>
        
        <section class="agreement-section">
          <h2>9. {{ $t('agreement.disputes.title') || '争议解决' }}</h2>
          <p>{{ $t('agreement.disputes.content1') || '本协议受中华人民共和国法律管辖。' }}</p>
          <p>{{ $t('agreement.disputes.content2') || '因本协议引起的或与之相关的任何争议，双方应首先通过友好协商解决。如协商不成，任何一方均可向有管辖权的法院提起诉讼。' }}</p>
        </section>
        
        <section class="agreement-section">
          <h2>10. {{ $t('agreement.changes.title') || '协议变更' }}</h2>
          <p>{{ $t('agreement.changes.content1') || '我们可能会不时修改本协议。修改后的协议将在我们的网站上发布，并在发布时生效。' }}</p>
          <p>{{ $t('agreement.changes.content2') || '继续使用我们的服务即表示您接受修改后的协议。如果您不同意修改内容，请停止使用我们的服务。' }}</p>
        </section>
        
        <section class="agreement-section">
          <h2>11. {{ $t('agreement.contact.title') || '联系我们' }}</h2>
          <p>{{ $t('agreement.contact.content') || '如果您对本协议有任何疑问，请通过以下方式联系我们：' }}</p>
          <p>Email: <EMAIL></p>
        </section>
      </div>
    </div>
  </div>
  
  <Footer />
</template>

<script setup>
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const breadcrumbs = [{ label: t('title.userAgreement') || '用户协议', to: '/agreement' }];
const lastUpdated = '2023-12-01';
</script>

<style lang="scss" scoped>
.agreement-page {
  max-width: 1200px;
  width: 100%;
  margin: 30px auto 50px;
  padding: 0 20px;
}

.agreement-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.agreement-header {
  margin-bottom: 30px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 20px;
}

.agreement-title {
  font-size: 2rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 10px;
  text-align: center;
}

.agreement-date {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.agreement-content {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
}

.agreement-section {
  margin-bottom: 30px;
  
  h2 {
    font-size: 1.5rem;
    font-weight: 500;
    color: #1976d2;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
  }
  
  p {
    margin-bottom: 15px;
  }
  
  ul {
    margin-left: 20px;
    margin-bottom: 15px;
    
    li {
      margin-bottom: 8px;
    }
  }
}

/* 平板电脑样式 */
@media (max-width: 1023px) {
  .agreement-container {
    padding: 30px;
  }
  
  .agreement-title {
    font-size: 1.8rem;
  }
  
  .agreement-section {
    h2 {
      font-size: 1.3rem;
    }
  }
}

/* 手机样式 */
@media (max-width: 599px) {
  .agreement-page {
    margin: 20px auto 30px;
    padding: 0 10px;
  }
  
  .agreement-container {
    padding: 20px;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
  }
  
  .agreement-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
  }
  
  .agreement-title {
    font-size: 1.5rem;
  }
  
  .agreement-content {
    font-size: 14px;
  }
  
  .agreement-section {
    margin-bottom: 20px;
    
    h2 {
      font-size: 1.2rem;
      margin-bottom: 10px;
    }
    
    p {
      margin-bottom: 10px;
    }
  }
}
</style>
