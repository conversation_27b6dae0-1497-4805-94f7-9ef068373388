<template>
  <Header />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />

  <div class="diy-order-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <q-icon name="build" size="32px" color="primary" class="q-mr-sm" />
            自定义订单
          </h1>
          <p class="page-subtitle">找不到想要的商品？告诉我们您的需求，我们帮您代购！</p>
        </div>
      </div>
    </div>

    <!-- 操作步骤指南 -->
    <div class="steps-guide">
      <q-card flat bordered class="steps-card">
        <q-card-section class="q-pb-none">
          <div class="steps-header">
            <q-icon name="timeline" size="20px" color="primary" class="q-mr-xs" />
            <span class="text-subtitle1 text-weight-medium">操作流程</span>
          </div>
        </q-card-section>
        <q-card-section>
          <div class="steps-container">
            <div class="step-item" :class="{ active: currentStep >= 1 }">
              <div class="step-circle">
                <q-icon name="edit" size="16px" />
              </div>
              <div class="step-content">
                <div class="step-title">填写商品信息</div>
                <div class="step-desc">提供商品链接和详细要求</div>
              </div>
            </div>
            <div class="step-divider"></div>
            <div class="step-item" :class="{ active: currentStep >= 2 }">
              <div class="step-circle">
                <q-icon name="payment" size="16px" />
              </div>
              <div class="step-content">
                <div class="step-title">确认并支付</div>
                <div class="step-desc">核对信息并完成支付</div>
              </div>
            </div>
            <div class="step-divider"></div>
            <div class="step-item" :class="{ active: currentStep >= 3 }">
              <div class="step-circle">
                <q-icon name="local_shipping" size="16px" />
              </div>
              <div class="step-content">
                <div class="step-title">等待发货</div>
                <div class="step-desc">我们为您采购并发货</div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <q-form @submit="onSubmit" class="diy-form">
        <!-- 商品详情部分 -->
        <q-card flat bordered class="form-section">
          <q-card-section class="section-header">
            <div class="section-title">
              <q-icon name="inventory_2" size="20px" color="primary" class="q-mr-xs" />
              <span class="text-h6 text-weight-medium">商品详情</span>
            </div>
            <q-separator class="q-mt-sm" />
          </q-card-section>

          <q-card-section class="form-fields">
            <!-- 商品名称 -->
            <div class="field-group">
              <label class="field-label required">商品名称 *</label>
              <q-input
                v-model="formData.name"
                outlined
                placeholder="请输入您想要购买的商品名称"
                :rules="[val => !!val || '请输入商品名称']"
                class="form-input"
                bg-color="white"
              >
                <template #prepend>
                  <q-icon name="shopping_bag" color="grey-6" />
                </template>
              </q-input>
            </div>

            <!-- 商品链接 -->
            <div class="field-group">
              <label class="field-label required">商品链接 *</label>
              <q-input
                v-model="formData.sourceLink"
                outlined
                placeholder="请粘贴淘宝、1688、京东等平台的商品链接"
                :rules="[val => !!val || '请输入商品链接']"
                class="form-input"
                bg-color="white"
              >
                <template #prepend>
                  <q-icon name="link" color="grey-6" />
                </template>
                <template #append>
                  <q-btn
                    flat
                    dense
                    icon="content_paste"
                    color="primary"
                    @click="pasteFromClipboard"
                    class="paste-btn"
                  >
                    <q-tooltip>粘贴链接</q-tooltip>
                  </q-btn>
                </template>
              </q-input>
              <div class="field-hint">
                <q-icon name="info" size="14px" color="grey-6" class="q-mr-xs" />
                <span class="text-caption text-grey-7">支持淘宝、天猫、1688、京东等主流电商平台</span>
              </div>
            </div>

            <!-- 规格说明 -->
            <div class="field-group">
              <label class="field-label">规格说明</label>
              <q-input
                v-model="formData.specifications"
                outlined
                type="textarea"
                rows="3"
                placeholder="请输入商品规格、颜色、尺寸等详细信息"
                class="form-input"
                bg-color="white"
              >
                <template #prepend>
                  <q-icon name="description" color="grey-6" />
                </template>
              </q-input>
            </div>

            <!-- 商品备注 -->
            <div class="field-group">
              <label class="field-label">商品备注</label>
              <q-input
                v-model="formData.memo"
                outlined
                type="textarea"
                rows="4"
                placeholder="请输入特殊要求或备注信息，如包装要求、发货时间等"
                class="form-input"
                bg-color="white"
              >
                <template #prepend>
                  <q-icon name="note" color="grey-6" />
                </template>
              </q-input>
            </div>

            <!-- 上传图片 -->
            <div class="field-group">
              <label class="field-label">商品图片</label>
              <div class="upload-section">
                <div class="uploaded-images" v-if="uploadedImages.length > 0">
                  <div
                    v-for="(image, index) in uploadedImages"
                    :key="index"
                    class="image-item"
                  >
                    <q-img :src="image.url" class="uploaded-image" />
                    <q-btn
                      round
                      dense
                      flat
                      icon="close"
                      color="negative"
                      class="remove-btn"
                      @click="removeImage(index)"
                    />
                  </div>
                </div>

                <q-btn
                  outline
                  color="primary"
                  icon="cloud_upload"
                  label="上传图片"
                  @click="triggerFileUpload"
                  class="upload-btn"
                />

                <input
                  ref="fileInput"
                  type="file"
                  multiple
                  accept="image/*"
                  style="display: none"
                  @change="onFileSelect"
                />

                <div class="upload-hint">
                  <q-icon name="info" size="14px" color="grey-6" class="q-mr-xs" />
                  <span class="text-caption text-grey-7">支持JPG、PNG格式，单张图片不超过5MB</span>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- 费用部分 -->
        <q-card flat bordered class="form-section">
          <q-card-section class="section-header">
            <div class="section-title">
              <q-icon name="payments" size="20px" color="primary" class="q-mr-xs" />
              <span class="text-h6 text-weight-medium">费用信息</span>
            </div>
            <q-separator class="q-mt-sm" />
          </q-card-section>

          <q-card-section class="form-fields">
            <!-- 单价 -->
            <div class="field-group">
              <label class="field-label required">商品单价(CNY) *</label>
              <q-input
                v-model.number="formData.price"
                outlined
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                :rules="[val => val > 0 || '请输入有效的单价']"
                class="form-input"
                @update:model-value="calculateTotal"
                bg-color="white"
                suffix="CNY"
              >
                <template #prepend>
                  <q-icon name="currency_yuan" color="grey-6" />
                </template>
              </q-input>
              <div class="field-hint">
                <q-icon name="info" size="14px" color="orange" class="q-mr-xs" />
                <span class="text-caption text-orange">如果价格未知，请填写10 CNY下单，等待客服确认准确金额</span>
              </div>
            </div>

            <!-- 数量 -->
            <div class="field-group">
              <label class="field-label required">购买数量 *</label>
              <div class="quantity-container">
                <q-btn
                  flat
                  round
                  icon="remove"
                  color="primary"
                  @click="decreaseQuantity"
                  :disable="formData.count <= 1"
                  class="quantity-btn"
                />
                <q-input
                  v-model.number="formData.count"
                  outlined
                  type="number"
                  min="1"
                  class="quantity-input"
                  @update:model-value="calculateTotal"
                  bg-color="white"
                  readonly
                />
                <q-btn
                  flat
                  round
                  icon="add"
                  color="primary"
                  @click="increaseQuantity"
                  class="quantity-btn"
                />
              </div>
            </div>

            <!-- 国内运费 -->
            <div class="field-group">
              <label class="field-label required">国内运费 *</label>
              <q-input
                v-model.number="formData.freight"
                outlined
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                :rules="[val => val >= 0 || '请输入有效的运费']"
                class="form-input"
                @update:model-value="calculateTotal"
                bg-color="white"
                suffix="CNY"
              >
                <template #prepend>
                  <q-icon name="local_shipping" color="grey-6" />
                </template>
              </q-input>
              <div class="field-hint">
                <q-icon name="info" size="14px" color="grey-6" class="q-mr-xs" />
                <span class="text-caption text-grey-7">从卖家到我们仓库的运费，如不确定可填写0</span>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- 费用总计 -->
        <q-card flat bordered class="total-section">
          <q-card-section>
            <div class="total-header">
              <q-icon name="calculate" size="20px" color="primary" class="q-mr-xs" />
              <span class="text-h6 text-weight-medium">费用总计</span>
            </div>
            <q-separator class="q-my-sm" />

            <div class="total-breakdown">
              <div class="total-row">
                <span class="total-label">商品费用：</span>
                <span class="total-value">¥{{ (formData.price * formData.count).toFixed(2) }}</span>
              </div>
              <div class="total-row">
                <span class="total-label">国内运费：</span>
                <span class="total-value">¥{{ formData.freight.toFixed(2) }}</span>
              </div>
              <q-separator class="q-my-sm" />
              <div class="total-row final-total">
                <span class="total-label">合计金额：</span>
                <span class="total-amount">¥{{ totalAmount.toFixed(2) }}</span>
              </div>
            </div>

            <div class="total-note">
              <q-icon name="info" size="14px" color="grey-6" class="q-mr-xs" />
              <span class="text-caption text-grey-7">不含国际运费，实际费用以客服确认为准</span>
            </div>
          </q-card-section>
        </q-card>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <q-btn
            type="submit"
            color="primary"
            size="lg"
            icon="shopping_cart"
            class="submit-btn"
            :loading="submitting"
            :disable="!isFormValid"
          >
            添加到购物车
          </q-btn>
          <q-btn
            color="deep-orange"
            size="lg"
            icon="flash_on"
            class="buy-now-btn"
            :loading="submitting"
            :disable="!isFormValid"
            @click="buyNow"
          >
            立即购买
          </q-btn>
        </div>
      </q-form>
    </div>

    <!-- 服务说明 -->
    <q-card flat bordered class="service-info">
      <q-card-section>
        <div class="service-header">
          <q-icon name="help_outline" size="20px" color="primary" class="q-mr-xs" />
          <span class="text-h6 text-weight-medium">服务说明</span>
        </div>
        <q-separator class="q-my-sm" />

        <div class="service-content">
          <div class="service-item">
            <q-icon name="check_circle" color="positive" class="q-mr-sm" />
            <span>支持淘宝、天猫、1688、京东等主流电商平台代购</span>
          </div>
          <div class="service-item">
            <q-icon name="check_circle" color="positive" class="q-mr-sm" />
            <span>专业客服团队为您提供商品咨询和采购服务</span>
          </div>
          <div class="service-item">
            <q-icon name="check_circle" color="positive" class="q-mr-sm" />
            <span>安全可靠的第三方物流配送服务</span>
          </div>
          <div class="service-item">
            <q-icon name="check_circle" color="positive" class="q-mr-sm" />
            <span>价格透明，无隐藏费用</span>
          </div>
        </div>

        <div class="service-links">
          <q-btn flat color="primary" icon="book" label="购物代理指南" />
          <q-btn flat color="primary" icon="support_agent" label="联系客服" />
          <q-btn flat color="primary" icon="help" label="常见问题" />
        </div>
      </q-card-section>
    </q-card>
  </div>

  <Footer />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useCartStore } from '~~/store/cart';
import { useOrderConfirmStore } from '~/store/orderConfirm';
import CustomOrderApi from '~/composables/customOrderApi';
import { useI18n } from 'vue-i18n';

const router = useRouter();
const route = useRoute();

const cartStore = useCartStore();
const orderConfirmStore = useOrderConfirmStore();
const { t } = useI18n();

// 面包屑导航
const breadcrumbs = [
  { label: 'DIY订单', to: '/diy-order' }
];

// 当前步骤
const currentStep = ref(1);

// 表单数据
const formData = ref({
  name: '',
  sourceLink: '',
  specifications: '',
  memo: '',
  price: 0,
  count: 1,
  freight: 0,
});

// 上传的图片
const uploadedImages = ref([]);
const fileInput = ref(null);

// 状态
const submitting = ref(false);

// 计算属性
const totalAmount = computed(() => {
  return (formData.value.price * formData.value.count) + formData.value.freight;
});

const isFormValid = computed(() => {
  return formData.value.name.trim() !== '' &&
         formData.value.sourceLink.trim() !== '' &&
         formData.value.price > 0 &&
         formData.value.count > 0 &&
         formData.value.freight >= 0;
});

// 方法
const calculateTotal = () => {
  // 总计会自动通过computed属性计算
};

const decreaseQuantity = () => {
  if (formData.value.count > 1) {
    formData.value.count--;
    calculateTotal();
  }
};

const increaseQuantity = () => {
  formData.value.count++;
  calculateTotal();
};

const triggerFileUpload = () => {
  fileInput.value?.click();
};

const onFileSelect = async (event) => {
  const files = Array.from(event.target.files);
  
  for (const file of files) {
    try {
      const response = await CustomOrderApi.uploadProductImage(file);
      if (response.code === 0) {
        uploadedImages.value.push({
          url: response.data.url,
          id: response.data.id
        });
      }
    } catch (error) {
      console.error('图片上传失败:', error);
      useNuxtApp().$showNotify({ 
        msg: '图片上传失败，请重试', 
        type: 'negative' 
      });
    }
  }
  
  // 清空文件输入
  event.target.value = '';
};

const removeImage = (index) => {
  uploadedImages.value.splice(index, 1);
};

const pasteFromClipboard = async () => {
  try {
    const text = await navigator.clipboard.readText();
    if (text) {
      formData.value.sourceLink = text;
      // onProductLinkBlur();
    }
  } catch (error) {
    console.error('粘贴失败:', error);
    useNuxtApp().$showNotify({
      msg: '粘贴失败，请手动输入链接',
      type: 'warning'
    });
  }
};

const onProductLinkBlur = async () => {
  if (formData.value.sourceLink.trim()) {
    try {
      // 验证链接并获取商品建议
      const response = await CustomOrderApi.getProductSuggestions(formData.value.sourceLink);
      if (response.code === 0 && response.data) {
        // 自动填充商品信息
        if (response.data.name && !formData.value.name) {
          formData.value.name = response.data.name;
        }
        if (response.data.specifications && !formData.value.specifications) {
          formData.value.specifications = response.data.specifications;
        }

        // 更新当前步骤
        currentStep.value = 2;
      }
    } catch (error) {
      console.error('获取商品建议失败:', error);
    }
  }
};

const onSubmit = async () => {
  submitting.value = true;
  try {
    const orderData = {
      ...formData.value,
      price: Math.round(formData.value.price * 100), // 转换为分
      freight: Math.round(formData.value.freight * 100), // 转换为分
      images: uploadedImages.value.map(img => img.id),
    };

        //转换自定义商品为本地商品
    const{code,data} = await CustomOrderApi.customProductTransition(orderData);
    if(code === 0){
      //添加商品到购物车
      console.log('自定义商品转换成功：',data);
      
      // 代购商品添加到购物车
      const cartItem = {
        id: 0,
        type: 2, //自定义商品
        count: orderData.count || 1,
        selected: true,
        storeName: data.shopName || 'Shop Name',
        price: data.price,
        memo: orderData.memo || '',
        spu: {
          id: data.id,
          name: data.name,
          picUrl: data.picUrl,
          categoryId: data.categoryId,
          freight: data.freight,
          source: data.source,
        },
        sku: {
          id: data.skus[0].id, // 代购商品使用商品ID作为SKU ID
          picUrl: data.skus[0].picUrl || '',
          price: data.skus[0].price,
          stock: data.skus[0].stock,
          properties: data.skus[0].properties,
        },
      };
      try {
        // 调用 API
        await cartStore.addAgentProduct(cartItem);
        useNuxtApp().$showNotify({ msg: '加入购物车' });
        // 跳转到购物车页面
        router.push('/cart');
      } catch (error) {
        console.error('Failed to add to cart:', error);
        useNuxtApp().$showNotify({ msg: '添加失败', type: 'negative' });
      }


    }else{
      useNuxtApp().$showNotify({ 
        msg: error.message || '操作失败，请重试', 
        type: 'negative' 
      });
    }
    
  } catch (error) {
    console.error('创建订单失败:', error);
    useNuxtApp().$showNotify({ 
      msg: error.message || '创建订单失败，请重试', 
      type: 'negative' 
    });
  } finally {
    submitting.value = false;
  }
};

const buyNow = async () => {
  submitting.value = true;
  
  try {
    const orderData = {
      ...formData.value,
      price: Math.round(formData.value.price * 100),
      freight: Math.round(formData.value.freight * 100),
      images: uploadedImages.value.map(img => img.id),
    };

    //转换自定义商品为本地商品
    const{code,data} = await CustomOrderApi.customProductTransition(orderData);
    if(code === 0){
      //添加商品到购物车
      console.log('自定义商品转换成功：',data);

       //把已选的商品ID存储到订单确认页的store
      const cartItem = prepareSubmitData(orderData,data);
      cartItem.id = -1; //防止传0被拦截
      console.log('buyNow:', cartItem);
      orderConfirmStore.source = 'direct';
      orderConfirmStore.directItem = cartItem;
      // 跳转到订单确认页面
      navigateTo('/order/confirm');
    }else{
      useNuxtApp().$showNotify({ 
        msg: error.message || '操作失败，请重试', 
        type: 'negative' 
      });
    }

  } catch (error) {
    console.error('立即购买失败:', error);
    useNuxtApp().$showNotify({ 
      msg: error.message || '操作失败，请重试', 
      type: 'negative' 
    });
  } finally {
    submitting.value = false;
  }
};

function prepareSubmitData(orderData,data) {
  return {
    id: 0,
    count: formData.value.count || 1,
    selected: true,
    price: data.price,
    memo: orderData.memo || '',
    spu: {
      id: data.id,
      name: data.name,
      picUrl: data.picUrl,
      categoryId: data.categoryId,
      price: data.price,
      shopName:data.shopName,
      freight:data.freight,
      sorce:data.source,
    },
    sku: {
      id: data.skus[0].id,
      picUrl: data.skus[0].picUrl || '',
      price: data.skus[0].price,
      stock: data.skus[0].stock,
      properties: data.skus[0].properties,
    },
  };
}


// 页面初始化
onMounted(() => {
  // 如果从搜索页面跳转过来，可能会有预填充的数据

  // 预填充商品链接
  if (route.query.productLink) {
    formData.value.sourceLink = decodeURIComponent(route.query.productLink);
    // onProductLinkBlur();
  }

  // 预填充商品名称
  if (route.query.productName) {
    formData.value.name = decodeURIComponent(route.query.productName);
  }
});
</script>

<style lang="scss" scoped>
.diy-order-page {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px 16px;
  background-color: #fafafa;
}

// 页面标题
.page-header {
  margin-bottom: 30px;

  .header-content {
    text-align: center;

    .title-section {
      .page-title {
        font-size: 32px;
        font-weight: bold;
        color: #333;
        margin: 0 0 10px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .page-subtitle {
        font-size: 16px;
        color: #666;
        margin: 0;
      }
    }
  }
}

// 步骤指南
.steps-guide {
  margin-bottom: 30px;

  .steps-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .steps-header {
      display: flex;
      align-items: center;
    }

    .steps-container {
      display: flex;
      align-items: center;
      justify-content: space-between;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 20px;
      }

      .step-item {
        display: flex;
        align-items: center;
        flex: 1;
        opacity: 0.6;
        transition: all 0.3s ease;

        &.active {
          opacity: 1;

          .step-circle {
            background-color: #0073e6;
            color: white;
          }
        }

        .step-circle {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-color: #e0e0e0;
          color: #666;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          transition: all 0.3s ease;
        }

        .step-content {
          .step-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
          }

          .step-desc {
            font-size: 12px;
            color: #666;
          }
        }
      }

      .step-divider {
        width: 40px;
        height: 2px;
        background-color: #e0e0e0;
        margin: 0 20px;

        @media (max-width: 768px) {
          width: 2px;
          height: 20px;
          margin: 10px 0;
        }
      }
    }
  }
}

// 表单容器
.form-container {
  .diy-form {
    display: flex;
    flex-direction: column;
    gap: 30px;
  }
}

// 表单卡片
.form-section {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .section-header {
    background-color: #f8f9fa;

    .section-title {
      display: flex;
      align-items: center;
    }
  }
}

// 表单字段
.form-fields {
  .field-group {
    margin-bottom: 25px;

    .field-label {
      display: block;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;

      &.required {
        color: #0073e6;
      }
    }

    .form-input {
      width: 100%;

      :deep(.q-field__control) {
        border-radius: 8px;
      }
    }

    .field-hint {
      display: flex;
      align-items: center;
      margin-top: 5px;
    }

    // 数量选择器
    .quantity-container {
      display: flex;
      align-items: center;
      gap: 0;
      max-width: 200px;

      .quantity-btn {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        width: 40px;
        height: 40px;

        &:first-child {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
          border-right: none;
        }

        &:last-child {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
          border-left: none;
        }
      }

      .quantity-input {
        flex: 1;
        text-align: center;

        :deep(.q-field__control) {
          border-radius: 0;
        }

        :deep(.q-field__native) {
          text-align: center;
        }
      }
    }
  }
}

// 图片上传
.upload-section {
  .uploaded-images {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;

    .image-item {
      position: relative;
      width: 80px;
      height: 80px;

      .uploaded-image {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        object-fit: cover;
        border: 2px solid #e0e0e0;
      }

      .remove-btn {
        position: absolute;
        top: -5px;
        right: -5px;
        background: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
    }
  }

  .upload-btn {
    margin-bottom: 10px;
  }

  .upload-hint {
    display: flex;
    align-items: center;
  }
}

// 费用总计
.total-section {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .total-header {
    display: flex;
    align-items: center;
  }

  .total-breakdown {
    .total-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .total-label {
        color: #666;
        font-size: 14px;
      }

      .total-value {
        color: #333;
        font-weight: 500;
      }

      &.final-total {
        .total-label {
          color: #333;
          font-weight: 600;
          font-size: 16px;
        }

        .total-amount {
          color: #0073e6;
          font-size: 20px;
          font-weight: bold;
        }
      }
    }
  }

  .total-note {
    display: flex;
    align-items: center;
    margin-top: 10px;
  }
}

// 操作按钮
.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;

  @media (max-width: 768px) {
    flex-direction: column;
  }

  .submit-btn,
  .buy-now-btn {
    min-width: 180px;
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    text-transform: none;
  }

  .submit-btn {
    background-color: #0073e6;

    &:hover {
      background-color: #005bb5;
    }
  }

  .buy-now-btn {
    background-color: #ff6b35;

    &:hover {
      background-color: #e55a2b;
    }
  }
}

// 服务信息
.service-info {
  margin-top: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .service-header {
    display: flex;
    align-items: center;
  }

  .service-content {
    .service-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      font-size: 14px;
      color: #333;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .service-links {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      justify-content: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .diy-order-page {
    padding: 15px 10px;
  }

  .page-header {
    .header-content {
      .title-section {
        .page-title {
          font-size: 24px;
          flex-direction: column;
          gap: 10px;
        }

        .page-subtitle {
          font-size: 14px;
        }
      }
    }
  }

  .form-fields {
    .field-group {
      .quantity-container {
        max-width: 100%;
      }
    }
  }

  .action-buttons {
    .submit-btn,
    .buy-now-btn {
      min-width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .page-header {
    .header-content {
      .title-section {
        .page-title {
          font-size: 20px;
        }
      }
    }
  }
}
</style>
