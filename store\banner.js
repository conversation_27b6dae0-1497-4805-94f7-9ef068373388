import { defineStore } from 'pinia';
import PromotionApi from '~/composables/promotionApi';

export const useBannerStore = defineStore('banner', {
  state: () => ({
    banners: [], // 存储所有的 banner 数据
    isLoaded: false, // 防止重复加载
  }),
  getters: {
    // 根据 tag 字段分类数据
    getBannersByTag: (state) => (tag) => {
      return state.banners.filter((banner) => banner.tag === tag);
    },
  },
  actions: {
    async fetchBanners() {
      console.log('fetchBanners');
      if (this.isLoaded) return; // 如果已经加载过，直接返回
      try {
        const response = await PromotionApi.getBannerList({ position: 1 });
        const rawBanners = response.data; // 获取后端返回的原始数据
        // console.log('rawBanners', rawBanners);
        this.banners = this.formatBanners(rawBanners);

        this.isLoaded = true; // 设置为已加载
      } catch (error) {
        console.error('Failed to fetch banners:', error);
      }
    },

    // 格式化后端返回的数据
    formatBanners(rawBanners) {
      return rawBanners.map((banner) => ({
        imagepath: banner.picUrl, // 映射图片路径
        title: banner.title, // 映射标题
        subtitle: banner.subTitle, // 映射副标题
        alignclass: banner.alignClass || 'p-left', // 设置默认样式类
        link: banner.url || '/', // 默认链接
        tag: banner.tag || 'default', // 使用后端的 tag 字段
      }));
    },
  },
});
