<template>
  <div>
    <q-dialog v-model="showDialog" persistent>
      <q-card class="stock-selector-dialog" style="width: 900px; max-width: 95vw; max-height: 90vh">
        <q-card-section class="row items-center bg-primary text-white q-py-sm">
          <div class="text-subtitle1 text-weight-medium">选择库存商品</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pa-sm">
          <!-- 搜索框 -->
          <div class="row justify-between items-center q-mb-sm">
            <div class="col-12 col-sm-6">
              <div class="search-container">
                <q-input v-model="searchText" outlined dense clearable placeholder="输入商品名称搜索" @keyup.enter="handleSearch" class="search-input">
                  <template #append>
                    <q-btn flat dense icon="search" @click="handleSearch" />
                  </template>
                </q-input>
              </div>
            </div>
            <div class="col-12 col-sm-6 text-right">
              <div class="text-caption text-grey-8">
                已选择 <span class="text-primary text-weight-medium">{{ selected.length }}</span> 件商品
              </div>
            </div>
          </div>

          <!-- 库存列表 -->
          <div class="stock-list">
            <!-- 桌面视图表格 -->
            <div v-if="!$q.screen.lt.sm">
              <q-table
                :rows="stockList"
                :columns="columns"
                row-key="id"
                selection="multiple"
                v-model:selected="selected"
                :pagination="pagination"
                :loading="loading"
                binary-state-sort
                hide-pagination
                spinner-color="primary"
                class="stock-table"
                dense>
                <template #loading>
                  <div class="absolute-full bg-white bg-opacity-60 flex flex-center" style="z-index: 1">
                    <div class="column items-center">
                      <q-spinner color="primary" size="3em" />
                      <div class="q-mt-sm text-primary text-body1">加载中...</div>
                    </div>
                  </div>
                </template>

                <!-- 自定义表头模板 -->
                <template #header="props">
                  <q-tr :props="props">
                    <q-th auto-width class="text-center">
                      <q-checkbox size="xs" v-model="props.selected" />
                    </q-th>
                    <q-th class="product-column">商品信息</q-th>
                    <q-th class="text-center">数量</q-th>
                    <q-th class="text-center">重量(kg)</q-th>
                    <q-th class="text-center">体积(m³)</q-th>
                    <q-th class="text-center">入库日期</q-th>
                  </q-tr>
                </template>

                <template #body="props">
                  <q-tr :props="props" class="cursor-pointer" @click="toggleRowSelection(props)">
                    <q-td auto-width class="text-center" @click.stop>
                      <q-checkbox v-model="props.selected" size="xs" />
                    </q-td>
                    <q-td class="product-column">
                      <div class="row items-start">
                        <q-img :src="getThumbnailUrl(props.row.picUrl, '60x60')" alt="商品图片" width="60px" height="60px" class="rounded-borders q-mr-sm" spinner-color="primary" />
                        <div class="column no-wrap product-info">
                          <div class="text-weight-medium text-dark q-mb-xs product-name ellipsis-3-lines">
                            {{ props.row.spuName }}
                          </div>
                          <div class="text-caption text-grey-8">{{ formattedProperties(props.row.properties) }}</div>
                        </div>
                      </div>
                    </q-td>
                    <q-td class="text-center">
                      {{ props.row.count }}
                    </q-td>
                    <q-td class="text-center">
                      {{ props.row.weight }}
                    </q-td>
                    <q-td class="text-center">
                      {{ props.row.volume }}
                    </q-td>
                    <q-td class="text-center">
                      {{ formatTimestampToWesternDate(props.row.inTime) }}
                    </q-td>
                  </q-tr>
                </template>
              </q-table>

              <!-- 分页控件 -->
              <div class="row justify-between items-center q-mt-sm flex-wrap" v-if="pagination.rowsNumber > 0">
                <div class="col-12 col-sm-auto q-mb-sm q-mb-sm-none">
                  <div class="row justify-end justify-sm-start">
                    <div class="text-caption text-grey-8">
                      共 {{ pagination.rowsNumber }} 条记录，第 {{ pagination.page }} / {{ Math.ceil(pagination.rowsNumber / pagination.rowsPerPage) || 1 }} 页
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-auto">
                  <div class="row justify-end">
                    <q-pagination
                      v-model="pagination.page"
                      :max="Math.ceil(pagination.rowsNumber / pagination.rowsPerPage)"
                      :max-pages="$q.screen.lt.sm ? 3 : 5"
                      boundary-links
                      direction-links
                      dense
                      @update:model-value="onPageChange" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 移动端卡片视图 -->
            <div v-else class="mobile-stock-list">
              <div v-if="loading" class="column items-center q-py-md">
                <q-spinner color="primary" size="2em" />
                <div class="q-mt-sm text-grey-7 text-caption">加载中...</div>
              </div>

              <div v-else-if="stockList.length === 0" class="column items-center q-py-md">
                <q-icon name="inventory_2" size="2em" color="grey-5" />
                <div class="text-grey-7 text-caption q-mt-sm">暂无库存商品</div>
              </div>

              <div v-else>
                <q-card v-for="item in stockList" :key="item.id" class="stock-card q-mb-sm cursor-pointer" flat bordered @click="toggleItemSelection(item)">
                  <q-card-section class="q-py-xs">
                    <div class="row justify-between items-center">
                      <q-checkbox v-model="selected" :val="item" size="xs" @click.stop />
                      <q-badge color="primary" class="q-px-xs q-py-xs text-caption">
                        {{ formatTimestampToWesternDate(item.inTime) }}
                      </q-badge>
                    </div>
                  </q-card-section>

                  <q-separator />

                  <q-card-section class="q-pa-sm">
                    <div class="row no-wrap">
                      <!-- 商品图片 -->
                      <div class="product-img">
                        <q-img :src="getThumbnailUrl(item.picUrl, '60x60')" style="width: 60px; height: 60px" class="rounded-borders" spinner-color="primary" alt="商品图片" />
                      </div>

                      <!-- 商品信息 -->
                      <div class="product-info q-ml-sm">
                        <div class="text-weight-medium product-name ellipsis-3-lines text-body2">{{ item.spuName }}</div>
                        <div class="text-caption text-grey-7 q-mt-xs">{{ formattedProperties(item.properties) }}</div>
                        <div class="row q-col-gutter-xs q-mt-xs">
                          <div class="col-6 text-caption">
                            <q-icon name="inventory" size="xs" /> 数量: <span class="text-weight-medium">{{ item.count }}</span>
                          </div>
                          <div class="col-6 text-caption">
                            <q-icon name="scale" size="xs" /> 重量: <span class="text-weight-medium">{{ item.weight }}</span>
                          </div>
                          <div class="col-6 text-caption">
                            <q-icon name="view_in_ar" size="xs" /> 体积: <span class="text-weight-medium">{{ item.volume }}</span>
                          </div>
                          <div class="col-6 text-caption">
                            <q-icon name="event" size="xs" /> 入库: <span class="text-weight-medium">{{ formatTimestampToWesternDate(item.inTime) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>

              <!-- 移动端分页 -->
              <div class="row justify-center q-mt-sm" v-if="stockList.length > 0">
                <q-pagination
                  v-model="pagination.page"
                  :max="Math.ceil(pagination.rowsNumber / pagination.rowsPerPage)"
                  :max-pages="3"
                  boundary-links
                  direction-links
                  dense
                  @update:model-value="onPageChange" />
              </div>
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right" class="bg-white q-py-sm q-px-md">
          <q-btn flat label="取消" color="grey-7" v-close-popup dense />
          <q-btn unelevated label="确认选择" color="primary" @click="confirmSelection" :disable="selected.length === 0" dense />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import StockApi from '~/composables/stockApi';
import { formattedProperties } from '~/utils/productUtils';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  initialSelected: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['update:modelValue', 'select']);

const $q = useQuasar();
const showDialog = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

// 搜索相关
const searchText = ref('');
const loading = ref(false);
const stockList = ref([]);
const selected = ref([]);

// 分页
const pagination = reactive({
  sortBy: 'desc',
  descending: false,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

// 表格列定义 - 注意：这里的列定义只用于数据绑定，实际显示使用自定义模板
const columns = [
  { name: 'selection', label: '', align: 'center', field: 'selection' },
  { name: 'spuName', label: '商品信息', align: 'left', field: 'spuName' },
  { name: 'count', align: 'center', label: '数量', field: 'count' },
  { name: 'weight', align: 'center', label: '重量(kg)', field: 'weight' },
  { name: 'volume', align: 'center', label: '体积(m³)', field: 'volume' },
  { name: 'inTime', align: 'center', label: '入库日期', field: 'inTime' },
];

// 初始化时加载数据
watch(showDialog, (newVal) => {
  if (newVal) {
    // 当弹窗打开时，加载数据
    getStockList();

    // 设置初始选中项
    if (props.initialSelected && props.initialSelected.length > 0) {
      selected.value = [...props.initialSelected];
    }
  }
});

// 获取库存列表
async function getStockList(page = pagination.page, rowsPerPage = pagination.rowsPerPage) {
  loading.value = true;

  try {
    const { code, data } = await StockApi.getStockPage({
      pageNo: page,
      pageSize: rowsPerPage,
      spuName: searchText.value,
    });

    if (code === 0) {
      stockList.value = data.list || [];
      pagination.rowsNumber = data.total;
    } else {
      $q.notify({
        color: 'negative',
        message: '获取库存列表失败',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('获取库存列表出错:', error);
    $q.notify({
      color: 'negative',
      message: '获取库存列表出错',
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
}

// 处理分页切换
function onPageChange(page) {
  pagination.page = page;
  getStockList(page, pagination.rowsPerPage);
}

// 处理搜索
function handleSearch() {
  pagination.page = 1;
  getStockList();
}

// 切换行选择状态
function toggleRowSelection(props) {
  props.selected = !props.selected;
}

// 切换项目选择状态
function toggleItemSelection(item) {
  const index = selected.value.findIndex((i) => i.id === item.id);
  if (index === -1) {
    // 如果不在选中列表中，添加
    selected.value.push(item);
  } else {
    // 如果已在选中列表中，移除
    selected.value.splice(index, 1);
  }
}

// 确认选择
function confirmSelection() {
  emit('select', selected.value);
  showDialog.value = false;
}

// 格式化日期
function formatTimestampToWesternDate(timestamp) {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
}

// 获取缩略图URL
function getThumbnailUrl(url, size = '80x80') {
  if (!url) return '';
  return url;
}
</script>

<style lang="scss" scoped>
.stock-selector-dialog {
  display: flex;
  flex-direction: column;
  max-height: 90vh;

  .stock-list {
    overflow-y: auto;
    flex: 1;
  }

  .stock-table {
    :deep(.q-table) {
      border-radius: 4px;

      thead tr th {
        position: sticky;
        top: 0;
        z-index: 1;
        background-color: #f5f5f5;
        font-size: 13px;
        padding: 8px 4px;
        font-weight: 500;
      }

      td {
        padding: 8px 4px;
        font-size: 13px;
      }

      tbody tr {
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(0, 0, 0, 0.03);
        }

        &.selected {
          background-color: rgba(25, 118, 210, 0.08);

          &:hover {
            background-color: rgba(25, 118, 210, 0.12);
          }
        }
      }
    }
  }

  .product-column {
    min-width: 300px;
    max-width: 400px;
  }

  .product-info {
    flex: 1;
    min-width: 0;
    max-width: calc(100% - 70px);

    .product-name {
      font-size: 13px;
      line-height: 1.3;
      max-height: 3.9em; /* 3行的高度 = 行高 * 3 */
      overflow: hidden;
      word-break: break-word;
    }
  }

  .search-container {
    display: flex;
    align-items: center;
    width: 100%;

    @media (max-width: 599px) {
      justify-content: space-between;
    }
  }

  .search-input {
    width: 100%;
    max-width: 280px;
    flex: 1;

    @media (max-width: 599px) {
      max-width: none;
      flex: 1;
    }
  }

  .mobile-stock-list {
    .stock-card {
      border-radius: 4px;
      overflow: hidden;
      transition: all 0.2s ease;
      border: 1px solid transparent;

      &:hover {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        background-color: rgba(0, 0, 0, 0.02);
      }

      &.selected,
      &.q-card--selected {
        border-color: rgba(25, 118, 210, 0.4);
        background-color: rgba(25, 118, 210, 0.05);

        &:hover {
          background-color: rgba(25, 118, 210, 0.08);
        }
      }

      .product-img {
        width: 60px;
        flex-shrink: 0;
      }

      .product-info {
        flex: 1;
        min-width: 0;
      }
    }
  }

  .ellipsis-3-lines {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    white-space: normal;
    max-height: 3.9em;
  }

  .ellipsis-2-lines {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    line-height: 1.3;
  }
}
</style>
