<template>
  <div class="help-sidebar">
    <q-list padding class="help-menu">
      <!-- 帮助中心首页链接 -->
      <q-item to="/help" clickable v-ripple :active="isHomePage" active-class="active-help-item" class="home-link">
        <q-item-section avatar>
          <q-icon name="home" color="primary" />
        </q-item-section>
        <q-item-section>
          {{ $t('help.sidebar.homePage') }}
        </q-item-section>
      </q-item>

      <q-separator class="q-my-sm" />

      <template v-for="(category, index) in categories" :key="index">
        <q-expansion-item :label="category.title" :icon="category.icon" :default-opened="isDefaultOpened(category)" header-class="help-category-header" expand-icon-class="text-primary">
          <q-list class="help-submenu">
            <q-item
              v-for="(item, itemIndex) in category.items"
              :key="itemIndex"
              :to="`/help/${category.code}/${item.code}`"
              clickable
              v-ripple
              :active="isActiveItem(category.code, item.code)"
              active-class="active-help-item">
              <q-item-section>
                {{ item.title }}
              </q-item-section>
            </q-item>
          </q-list>
        </q-expansion-item>
      </template>
    </q-list>

    <div class="contact-support">
      <q-separator class="q-my-md" />
      <p class="contact-title">{{ $t('help.contactSupport.title') }}</p>
      <q-btn color="primary" icon="headset_mic" :label="$t('help.contactSupport.button')" class="full-width" @click="contactSupport" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';

const props = defineProps({
  categories: {
    type: Array,
    required: true,
  },
  currentCategoryCode: {
    type: String,
    default: '',
  },
});

const $q = useQuasar();
const route = useRoute();
const { t } = useI18n();

// 判断是否为帮助中心首页
const isHomePage = computed(() => {
  return route.path === '/help' || route.path === '/help/';
});

// 判断当前激活的菜单项
const isActiveItem = (categoryId, itemId) => {
  return route.params.category === categoryId && route.params.item === itemId;
};

// 判断是否默认展开
const isDefaultOpened = (category) => {
  // 如果有当前分类Code，则展开当前分类
  if (props.currentCategoryCode) {
    return category.code === props.currentCategoryCode;
  }
  // 否则展开第一个分类
  return props.categories.indexOf(category) === 0;
};

// 联系客服
const contactSupport = () => {
  $q.dialog({
    title: t('help.contactDialog.title'),
    message: `
      <div class="contact-info">
        <div class="contact-item">
          <i class="material-icons q-mr-sm">phone</i>
          <span>客服热线：************（工作时间：9:00-22:00）</span>
        </div>
        <div class="contact-item">
          <i class="material-icons q-mr-sm">mail</i>
          <span>客服邮箱：<EMAIL></span>
        </div>
        <div class="contact-item">
          <i class="material-icons q-mr-sm">chat</i>
          <span>在线客服：点击网站右下角的客服图标</span>
        </div>
      </div>
    `,
    html: true,
    ok: {
      label: t('help.buttons.close'),
      flat: true,
      color: 'primary',
    },
    style: 'min-width: 350px',
  }).onOk(() => {
    // 关闭对话框后的操作
  });
};
</script>

<style lang="scss" scoped>
.help-sidebar {
  width: 280px;
  flex-shrink: 0;
  background-color: #f9f9f9;
  border-right: 1px solid #eee;
  padding: 20px 0;
}

.help-menu {
  .help-category-header {
    font-weight: bold;
    color: #333;
  }

  .home-link {
    border-radius: 4px;
    margin: 2px 8px 8px;
    padding: 8px 16px;
    font-weight: 500;

    &.active-help-item {
      background-color: rgba(25, 118, 210, 0.1);
      color: #1976d2;
    }
  }
}

.help-submenu {
  padding-left: 16px;

  .q-item {
    border-radius: 4px;
    margin: 2px 8px;
    padding: 8px 16px;

    &.active-help-item {
      background-color: rgba(25, 118, 210, 0.1);
      color: #1976d2;
      font-weight: 500;
    }
  }
}

.contact-support {
  padding: 0 16px;

  .contact-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
    text-align: center;
  }
}

@media (max-width: 1023px) {
  .help-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #eee;
  }
}
</style>
