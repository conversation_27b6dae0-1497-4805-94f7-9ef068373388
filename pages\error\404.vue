<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-code">404</div>
      <h1 class="error-title">{{ $t('error.page.titles.404') }}</h1>
      <p class="error-description">{{ $t('error.page.descriptions.404') }}</p>

      <div class="error-image">
        <img src="/images/error/404.svg" alt="404 Error" />
      </div>

      <div class="error-actions">
        <q-btn color="primary" icon="home" :label="$t('error.page.backToHome')" to="/" class="q-mr-md" />
        <q-btn outline color="primary" icon="search" :label="$t('error.page.search')" @click="openSearch" />
      </div>

      <div class="error-suggestions">
        <h3 class="suggestions-title">{{ $t('error.page.suggestions') }}</h3>
        <ul class="suggestions-list">
          <li><q-btn flat color="primary" to="/" :label="$t('error.page.homePage')" /></li>
          <li><q-btn flat color="primary" to="/products" :label="$t('error.page.productList')" /></li>
          <li><q-btn flat color="primary" to="/account" :label="$t('error.page.userCenter')" /></li>
          <li><q-btn flat color="primary" to="/help" :label="$t('error.page.helpCenter')" /></li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';

const router = useRouter();

// 打开搜索功能
const openSearch = () => {
  // 这里可以打开搜索弹窗或跳转到搜索页面
  router.push('/search');
};
</script>

<style lang="scss" scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  padding: 20px;
}

.error-container {
  max-width: 800px;
  width: 100%;
  text-align: center;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  padding: 40px 20px;
}

.error-code {
  font-size: 120px;
  font-weight: 900;
  color: #1976d2;
  line-height: 1;
  margin-bottom: 16px;
  text-shadow: 2px 2px 4px rgba(25, 118, 210, 0.2);
}

.error-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.error-description {
  font-size: 18px;
  color: #666;
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.error-image {
  max-width: 400px;
  margin: 0 auto 40px;

  img {
    width: 100%;
    height: auto;
  }
}

.error-actions {
  margin-bottom: 40px;
}

.error-suggestions {
  max-width: 500px;
  margin: 0 auto;
}

.suggestions-title {
  font-size: 18px;
  color: #333;
  margin-bottom: 16px;
}

.suggestions-list {
  list-style: none;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;

  li {
    margin: 0;
  }
}

@media (max-width: 767px) {
  .error-code {
    font-size: 80px;
  }

  .error-title {
    font-size: 24px;
  }

  .error-description {
    font-size: 16px;
  }

  .error-image {
    max-width: 280px;
  }

  .suggestions-title {
    font-size: 16px;
  }
}
</style>
