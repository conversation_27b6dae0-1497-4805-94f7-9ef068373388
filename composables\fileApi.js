/**
 * 文件上传API
 * 基于项目现有的上传实现，使用$fetch直接上传
 */

// 导入必要的依赖
import { useAuthStore } from '~/store/auth';

/**
 * 创建一个类似原始request.upload的方法
 * @param {Object} options - 上传选项
 * @param {string} options.url - 上传URL
 * @param {any} options.data - 上传数据（包含file字段）
 * @returns {Promise} 上传结果
 */
const requestUpload = async ({ url, data }) => {
  const formData = new FormData();

  // 如果data包含file字段，直接添加
  if (data.file) {
    formData.append('file', data.file);
  }

  // 添加其他字段（只有当值存在且不为null时才添加）
  Object.keys(data).forEach(key => {
    if (key !== 'file' && data[key] !== undefined && data[key] !== null) {
      formData.append(key, data[key]);
    }
  });

  // 获取基础配置
  const config = useRuntimeConfig();
  const baseURL = config.public.baseUrl + config.public.apiPath;
  const token = useAuthStore().useToken();

  try {
    const response = await $fetch(url, {
      method: 'POST',
      baseURL,
      body: formData,
      headers: {
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
        'tenant-id': globalConfig.tenantId,
      },
    });

    return response;
  } catch (error) {
    console.error('文件上传失败:', error);
    throw error;
  }
};

/**
 * 上传单个文件 - 模仿原始的updateFile方法
 * @param {Object} data - 包含file的数据对象
 * @returns {Promise} 上传结果
 */
export const updateFile = (data) => {
  return requestUpload({ url: '/infra/file/upload', data });
};

/**
 * 上传单个文件 - 简化接口
 * @param {File} file - 文件对象
 * @param {string} path - 文件路径（可选，不传则后端随机命名）
 * @returns {Promise} 上传结果
 */
export const uploadFile = (file, path = null) => {
  const data = { file };
  if (path) {
    data.path = path;
  }
  return updateFile(data);
};

/**
 * 批量上传文件
 * @param {Array<File>} files - 文件数组
 * @param {string} path - 文件路径（可选）
 * @returns {Promise<Array>} 上传结果数组
 */
export const uploadFiles = async (files, path = 'ticket') => {
  const uploadPromises = files.map(file => uploadFile(file, path));
  return Promise.all(uploadPromises);
};

/**
 * 删除文件
 * @param {number} id - 文件ID
 * @returns {Promise} 删除结果
 */
export const deleteFile = (id) => {
  return useClientDelete(`/infra/file/delete?id=${id}`);
};

/**
 * 获取文件列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 文件列表
 */
export const getFileList = (params = {}) => {
  return useClientGet('/infra/file/page', {
    params
  });
};

export default {
  uploadFile,
  uploadFiles,
  deleteFile,
  getFileList
};