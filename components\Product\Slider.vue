//滑动行商品列表展示
<template>
  <!-- 轮播商品 -->
  <div class="slider-products">
    <div class="module-title">
      <h4>{{ display.title }}</h4>
      <!-- <a :href="moreLink" class="more-link">
        {{ $t('more') }}
        <i class="iconfont icon-gengduo gengduo" />
      </a> -->
    </div>

    <!-- 桌面端轮播 (大屏幕) -->
    <div class="carousel-wrapper gt-md">
      <!-- 左侧导航按钮 -->
      <q-btn round flat dense icon="chevron_left" color="primary" @click="prevSlide" class="carousel-nav-btn carousel-nav-btn-left" />

      <q-carousel
        class="bg-grey-1 rounded-borders desktop-carousel"
        v-model="desktopSlide"
        transition-prev="slide-right"
        transition-next="slide-left"
        swipeable
        animated
        padding
        infinite
        @mousedown="startSwipe"
        @mouseup="endSwipe"
        @touchstart="startSwipe"
        @touchend="endSwipe"
        height="400px"
        :arrows="false">
        <q-carousel-slide v-for="(group, index) in desktopGroups" :key="index" :name="index + 1" class="column no-wrap q-pa-none">
          <div class="row fit justify-start items-center q-px-none">
            <div v-for="(product, idx) in group" :key="idx" class="product-item-desktop">
              <ProductBox :product="product" :index="index" />
            </div>
          </div>
        </q-carousel-slide>
      </q-carousel>

      <!-- 右侧导航按钮 -->
      <q-btn round flat dense icon="chevron_right" color="primary" @click="nextSlide" class="carousel-nav-btn carousel-nav-btn-right" />
    </div>

    <!-- 平板端轮播 (中等屏幕) -->
    <div class="carousel-wrapper sm lt-lg">
      <!-- 左侧导航按钮 -->
      <q-btn round flat dense icon="chevron_left" color="primary" @click="prevSlide" class="carousel-nav-btn carousel-nav-btn-left" />

      <q-carousel
        class="bg-grey-1 rounded-borders tablet-carousel"
        v-model="tabletSlide"
        transition-prev="slide-right"
        transition-next="slide-left"
        swipeable
        animated
        padding
        infinite
        @mousedown="startSwipe"
        @mouseup="endSwipe"
        @touchstart="startSwipe"
        @touchend="endSwipe"
        height="380px"
        :arrows="false">
        <q-carousel-slide v-for="(group, index) in tabletGroups" :key="index" :name="index + 1" class="column no-wrap q-pa-none">
          <div class="row fit justify-start items-center q-px-none">
            <div v-for="(product, idx) in group" :key="idx" class="product-item-tablet">
              <ProductBox :product="product" :index="index" />
            </div>
          </div>
        </q-carousel-slide>
      </q-carousel>

      <!-- 右侧导航按钮 -->
      <q-btn round flat dense icon="chevron_right" color="primary" @click="nextSlide" class="carousel-nav-btn carousel-nav-btn-right" />
    </div>

    <!-- 移动端轮播 (小屏幕) -->
    <div class="carousel-wrapper lt-sm">
      <!-- 左侧导航按钮 -->
      <q-btn round flat dense icon="chevron_left" color="primary" @click="prevSlide" class="carousel-nav-btn carousel-nav-btn-left" size="sm" />

      <q-carousel
        class="bg-grey-1 rounded-borders mobile-carousel"
        v-model="mobileSlide"
        transition-prev="slide-right"
        transition-next="slide-left"
        swipeable
        animated
        padding
        infinite
        @mousedown="startSwipe"
        @mouseup="endSwipe"
        @touchstart="startSwipe"
        @touchend="endSwipe"
        height="360px"
        :arrows="false">
        <q-carousel-slide v-for="(group, index) in mobileGroups" :key="index" :name="index + 1" class="column no-wrap q-pa-none">
          <div class="row fit justify-around items-center q-px-none">
            <div v-for="(product, idx) in group" :key="idx" class="product-item-mobile">
              <ProductBox :product="product" :index="index" />
            </div>
          </div>
        </q-carousel-slide>
      </q-carousel>

      <!-- 右侧导航按钮 -->
      <q-btn round flat dense icon="chevron_right" color="primary" @click="nextSlide" class="carousel-nav-btn carousel-nav-btn-right" size="sm" />
    </div>
  </div>
</template>

<script setup>
import { useResponsive } from '~/composables/useResponsive';

const props = defineProps({
  display: {
    type: Object,
    required: true,
    default: () => ({}), // 确保 prop 的默认值是一个对象
  },
  // title: { type: String, required: false, default: '' },
  // subTitle: { type: String, required: false, default: '' },
  // moreLink: { type: String, required: false, default: '' },
  // products: { type: Array, required: true }, // 商品列表
});

const { isMobile, isTablet, isDesktop } = useResponsive();

// 各屏幕尺寸轮播索引
const desktopSlide = ref(1);
const tabletSlide = ref(1);
const mobileSlide = ref(1);

// 滑动相关变量
const startX = ref(0);
const endX = ref(0);
const isSwiping = ref(false);

// 开始滑动
const startSwipe = (e) => {
  isSwiping.value = true;
  startX.value = e.touches ? e.touches[0].clientX : e.clientX;
};

// 结束滑动
const endSwipe = (e) => {
  if (!isSwiping.value) return;

  endX.value = e.changedTouches ? e.changedTouches[0].clientX : e.clientX;
  const diff = endX.value - startX.value;

  // 根据当前屏幕尺寸选择正确的轮播索引
  let currentSlide;
  let totalSlides;

  if (isDesktop.value) {
    currentSlide = desktopSlide.value;
    totalSlides = desktopGroups.value.length;
  } else if (isTablet.value) {
    currentSlide = tabletSlide.value;
    totalSlides = tabletGroups.value.length;
  } else {
    currentSlide = mobileSlide.value;
    totalSlides = mobileGroups.value.length;
  }

  // 向左滑动
  if (diff < -50 && currentSlide < totalSlides) {
    if (isDesktop.value) {
      desktopSlide.value = currentSlide + 1;
    } else if (isTablet.value) {
      tabletSlide.value = currentSlide + 1;
    } else {
      mobileSlide.value = currentSlide + 1;
    }
  }
  // 向右滑动
  else if (diff > 50 && currentSlide > 1) {
    if (isDesktop.value) {
      desktopSlide.value = currentSlide - 1;
    } else if (isTablet.value) {
      tabletSlide.value = currentSlide - 1;
    } else {
      mobileSlide.value = currentSlide - 1;
    }
  }

  isSwiping.value = false;
};

// 上一张幻灯片
const prevSlide = () => {
  // 根据当前屏幕尺寸选择正确的轮播索引
  if (isDesktop.value) {
    desktopSlide.value = desktopSlide.value > 1 ? desktopSlide.value - 1 : desktopGroups.value.length;
  } else if (isTablet.value) {
    tabletSlide.value = tabletSlide.value > 1 ? tabletSlide.value - 1 : tabletGroups.value.length;
  } else {
    mobileSlide.value = mobileSlide.value > 1 ? mobileSlide.value - 1 : mobileGroups.value.length;
  }
};

// 下一张幻灯片
const nextSlide = () => {
  // 根据当前屏幕尺寸选择正确的轮播索引
  if (isDesktop.value) {
    desktopSlide.value = desktopSlide.value < desktopGroups.value.length ? desktopSlide.value + 1 : 1;
  } else if (isTablet.value) {
    tabletSlide.value = tabletSlide.value < tabletGroups.value.length ? tabletSlide.value + 1 : 1;
  } else {
    mobileSlide.value = mobileSlide.value < mobileGroups.value.length ? mobileSlide.value + 1 : 1;
  }
};

// 将商品按每5个分组（桌面端大屏幕）
const desktopGroups = computed(() => {
  const groups = [];
  if (!props.display.spus || props.display.spus.length === 0) {
    return [];
  }

  const itemsPerGroup = 5; // 桌面端每组5个商品

  for (let i = 0; i < props.display.spus.length; i += itemsPerGroup) {
    groups.push(props.display.spus.slice(i, i + itemsPerGroup));
  }
  return groups;
});

// 将商品按每3个分组（平板端）
const tabletGroups = computed(() => {
  const groups = [];
  if (!props.display.spus || props.display.spus.length === 0) {
    return [];
  }

  const itemsPerGroup = 3; // 平板端每组3个商品

  for (let i = 0; i < props.display.spus.length; i += itemsPerGroup) {
    groups.push(props.display.spus.slice(i, i + itemsPerGroup));
  }
  return groups;
});

// 将商品按每2个分组（移动端）
const mobileGroups = computed(() => {
  const groups = [];
  if (!props.display.spus || props.display.spus.length === 0) {
    return [];
  }

  const itemsPerGroup = 2; // 移动端每组2个商品

  for (let i = 0; i < props.display.spus.length; i += itemsPerGroup) {
    groups.push(props.display.spus.slice(i, i + itemsPerGroup));
  }
  return groups;
});
</script>

<style lang="scss" scoped>
//轮播活动
.slider-products {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  margin-top: 10px;
  padding: 0;

  .module-title {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    position: relative;

    h4 {
      font-size: 24px;
      font-weight: bold;
      margin: 0;
      text-align: center;

      @media (max-width: 599px) {
        font-size: 20px;
      }
    }

    .more-link {
      position: absolute;
      right: 0;
      color: #888;
      font-size: 14px;
      text-decoration: none;
      transition: color 0.3s ease;

      &:hover {
        color: #333;
      }
      .gengduo {
        font-size: 12px;
      }
    }
  }

  // 轮播容器样式
  .desktop-carousel,
  .tablet-carousel,
  .mobile-carousel {
    overflow: hidden; /* 防止出现滚动条 */
    touch-action: pan-y; /* 允许垂直滚动，但水平滑动会被捕获为轮播事件 */
    cursor: grab; /* 显示抓取光标，提示用户可以滑动 */

    &:active {
      cursor: grabbing; /* 滑动时显示抓取中光标 */
    }
  }

  // 桌面端商品项样式
  .product-item-desktop {
    width: 20%;
    height: auto; /* 自适应高度 */
    overflow: hidden;
    padding: 0 5px; /* 减小内边距 */
  }

  // 平板端商品项样式
  .product-item-tablet {
    width: 33.33%;
    height: auto; /* 自适应高度 */
    overflow: hidden;
    padding: 0 5px; /* 减小内边距 */
  }

  // 移动端商品项样式
  .product-item-mobile {
    width: 48%;
    height: auto; /* 自适应高度 */
    overflow: hidden;
    padding: 0 2px; /* 减小内边距 */
  }

  // 轮播包装器，用于定位导航按钮
  .carousel-wrapper {
    position: relative;

    // 轮播导航按钮样式
    .carousel-nav-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      background: rgba(255, 255, 255, 0.7);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
      font-size: 1.2rem;
      width: 32px;
      height: 32px;

      &-left {
        left: 8px;
      }

      &-right {
        right: 8px;
      }
    }

    // 桌面端和平板端按钮样式
    &.gt-md,
    &.sm {
      .carousel-nav-btn {
        width: 32px;
        height: 32px;
        font-size: 1.4rem;
        background: rgba(255, 255, 255, 0.65);

        &-left {
          left: 0px;
        }

        &-right {
          right: 0px;
        }
      }
    }
  }
}

@media (max-width: 599px) {
  .slider-products {
    padding: 0 5px;
    margin-top: 5px;

    .product-item-mobile {
      height: auto; /* 自适应高度 */
    }

    .mobile-carousel {
      height: 360px; /* 调整轮播容器高度 */
    }
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .slider-products {
    .product-item-tablet {
      height: auto; /* 自适应高度 */
    }

    .tablet-carousel {
      height: 380px; /* 调整轮播容器高度 */
    }
  }
}
</style>
