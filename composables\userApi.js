const UserApi = {
  // 获得基本信息
  getUserInfo: () => {
    return useClientGet('/member/user/get', {
      custom: {
        showLoading: false,
        auth: true,
      },
    });
  },

  //重置密码
  resetUserPassword: (data) => {
    return useClientPut('/member/user/reset-password-mail', {
      body: data,
    });
  },

  // 修改基本信息
  updateUser: (data) => {
    return useClientPut('/member/user/update', {
      body: data,
      custom: {
        auth: true,
        showSuccess: true,
        successMsg: '更新成功',
      },
    });
  },
  // 修改用户手机
  updateUserMobile: (data) => {
    return useClientPut('/member/user/update-mobile', {
      body: data,
      custom: {
        loadingMsg: '验证中',
        showSuccess: true,
        successMsg: '修改成功',
      },
    });
  },
  // 修改密码
  updateUserPassword: (data) => {
    return useClientPut('/member/user/update-password', {
      body: data,
      custom: {
        loadingMsg: '验证中',
        showSuccess: true,
        successMsg: '修改成功',
      },
    });
  },
  // 重置密码
  // resetUserPassword: (data) => {
  //   return useClientPut('/member/user/reset-password', {
  //     body: data,
  //     custom: {
  //       loadingMsg: '验证中',
  //       showSuccess: true,
  //       successMsg: '修改成功',
  //     },
  //   });
  // },
};

export default UserApi;
