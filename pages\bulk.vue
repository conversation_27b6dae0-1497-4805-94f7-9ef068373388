<template>
  <!-- 公告栏和菜单 -->
  <Header />

  <div class="bulk-purchase-container q-py-md">
    <!-- 顶部横幅 -->
    <div class="banner-section">
      <div class="container">
        <div class="row items-center">
          <div class="col-12 col-md-6 q-pa-md">
            <h1 class="text-h4 text-weight-bold q-mb-md">{{ t('bulkPage.banner.title') }}</h1>
            <p class="text-subtitle1 q-mb-lg">{{ t('bulkPage.banner.subtitle') }}</p>
            <q-btn color="primary" :label="t('bulkPage.banner.button')" class="q-px-md" size="lg" icon-right="arrow_forward" @click="scrollToContact" />
          </div>
          <div class="col-12 col-md-6 text-center">
            <q-img src="/images/bulk-purchase.svg" :alt="t('bulkPurchase')" width="80%" class="q-mt-md q-mt-md-none" />
          </div>
        </div>
      </div>
    </div>

    <!-- 我们的优势 -->
    <div class="advantages-section q-py-xl">
      <div class="container">
        <div class="text-center q-mb-lg">
          <h2 class="text-h5 text-weight-bold">{{ t('bulkPage.advantages.title') }}</h2>
          <div class="separator"></div>
        </div>

        <div class="row q-col-gutter-md q-mt-md">
          <div class="col-12 col-sm-6 col-md-3">
            <q-card class="advantage-card">
              <q-card-section class="text-center">
                <q-icon name="savings" size="3.5rem" color="primary" />
                <h3 class="text-h6 q-mt-sm">{{ t('bulkPage.advantages.items.price.title') }}</h3>
                <p class="q-mb-none">{{ t('bulkPage.advantages.items.price.desc') }}</p>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-sm-6 col-md-3">
            <q-card class="advantage-card">
              <q-card-section class="text-center">
                <q-icon name="verified" size="3.5rem" color="primary" />
                <h3 class="text-h6 q-mt-sm">{{ t('bulkPage.advantages.items.quality.title') }}</h3>
                <p class="q-mb-none">{{ t('bulkPage.advantages.items.quality.desc') }}</p>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-sm-6 col-md-3">
            <q-card class="advantage-card">
              <q-card-section class="text-center">
                <q-icon name="inventory_2" size="3.5rem" color="primary" />
                <h3 class="text-h6 q-mt-sm">{{ t('bulkPage.advantages.items.variety.title') }}</h3>
                <p class="q-mb-none">{{ t('bulkPage.advantages.items.variety.desc') }}</p>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-sm-6 col-md-3">
            <q-card class="advantage-card">
              <q-card-section class="text-center">
                <q-icon name="support_agent" size="3.5rem" color="primary" />
                <h3 class="text-h6 q-mt-sm">{{ t('bulkPage.advantages.items.service.title') }}</h3>
                <p class="q-mb-none">{{ t('bulkPage.advantages.items.service.desc') }}</p>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </div>

    <!-- 适用场景 -->
    <div class="scenarios-section q-py-xl">
      <div class="container">
        <div class="text-center q-mb-lg">
          <h2 class="text-h5 text-weight-bold">{{ t('bulkPage.scenarios.title') }}</h2>
          <div class="separator"></div>
        </div>

        <div class="row q-col-gutter-lg q-mt-md">
          <div class="col-12 col-md-4">
            <q-card class="scenario-card">
              <q-img src="/images/corporate-gifts.jpg" height="200px">
                <div class="absolute-bottom text-subtitle1 text-center bg-primary text-white">{{ t('bulkPage.scenarios.items.gifts.title') }}</div>
              </q-img>
              <q-card-section>
                <p>{{ t('bulkPage.scenarios.items.gifts.desc') }}</p>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-md-4">
            <q-card class="scenario-card">
              <q-img src="/images/retail-supply.jpg" height="200px">
                <div class="absolute-bottom text-subtitle1 text-center bg-primary text-white">{{ t('bulkPage.scenarios.items.retail.title') }}</div>
              </q-img>
              <q-card-section>
                <p>{{ t('bulkPage.scenarios.items.retail.desc') }}</p>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-md-4">
            <q-card class="scenario-card">
              <q-img src="/images/event-supplies.jpg" height="200px">
                <div class="absolute-bottom text-subtitle1 text-center bg-primary text-white">{{ t('bulkPage.scenarios.items.events.title') }}</div>
              </q-img>
              <q-card-section>
                <p>{{ t('bulkPage.scenarios.items.events.desc') }}</p>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </div>

    <!-- 服务流程 -->
    <div class="process-section q-py-xl">
      <div class="container">
        <div class="text-center q-mb-lg">
          <h2 class="text-h5 text-weight-bold">{{ t('bulkPage.process.title') }}</h2>
          <div class="separator"></div>
        </div>

        <div class="row q-col-gutter-md q-mt-lg">
          <div class="col-12">
            <div class="process-timeline">
              <div class="process-step">
                <div class="process-icon">
                  <q-icon name="chat" color="white" size="1.5rem" />
                </div>
                <div class="process-content">
                  <h3 class="text-subtitle1 text-weight-bold">{{ t('bulkPage.process.steps.communication.title') }}</h3>
                  <p>{{ t('bulkPage.process.steps.communication.desc') }}</p>
                </div>
              </div>

              <div class="process-step">
                <div class="process-icon">
                  <q-icon name="description" color="white" size="1.5rem" />
                </div>
                <div class="process-content">
                  <h3 class="text-subtitle1 text-weight-bold">{{ t('bulkPage.process.steps.planning.title') }}</h3>
                  <p>{{ t('bulkPage.process.steps.planning.desc') }}</p>
                </div>
              </div>

              <div class="process-step">
                <div class="process-icon">
                  <q-icon name="handshake" color="white" size="1.5rem" />
                </div>
                <div class="process-content">
                  <h3 class="text-subtitle1 text-weight-bold">{{ t('bulkPage.process.steps.confirmation.title') }}</h3>
                  <p>{{ t('bulkPage.process.steps.confirmation.desc') }}</p>
                </div>
              </div>

              <div class="process-step">
                <div class="process-icon">
                  <q-icon name="shopping_cart" color="white" size="1.5rem" />
                </div>
                <div class="process-content">
                  <h3 class="text-subtitle1 text-weight-bold">{{ t('bulkPage.process.steps.procurement.title') }}</h3>
                  <p>{{ t('bulkPage.process.steps.procurement.desc') }}</p>
                </div>
              </div>

              <div class="process-step">
                <div class="process-icon">
                  <q-icon name="local_shipping" color="white" size="1.5rem" />
                </div>
                <div class="process-content">
                  <h3 class="text-subtitle1 text-weight-bold">{{ t('bulkPage.process.steps.logistics.title') }}</h3>
                  <p>{{ t('bulkPage.process.steps.logistics.desc') }}</p>
                </div>
              </div>

              <div class="process-step">
                <div class="process-icon">
                  <q-icon name="check_circle" color="white" size="1.5rem" />
                </div>
                <div class="process-content">
                  <h3 class="text-subtitle1 text-weight-bold">{{ t('bulkPage.process.steps.completion.title') }}</h3>
                  <p>{{ t('bulkPage.process.steps.completion.desc') }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 常见问题 -->
    <div class="faq-section q-py-xl">
      <div class="container">
        <div class="text-center q-mb-lg">
          <h2 class="text-h5 text-weight-bold">{{ t('bulkPage.faq.title') }}</h2>
          <div class="separator"></div>
        </div>

        <div class="row q-mt-md">
          <div class="col-12 col-md-10 offset-md-1">
            <q-list bordered separator class="rounded-borders">
              <q-expansion-item expand-separator icon="help_outline" :label="t('bulkPage.faq.questions.minQuantity.question')" header-class="text-primary">
                <q-card>
                  <q-card-section> {{ t('bulkPage.faq.questions.minQuantity.answer') }} </q-card-section>
                </q-card>
              </q-expansion-item>

              <q-expansion-item expand-separator icon="help_outline" :label="t('bulkPage.faq.questions.payment.question')" header-class="text-primary">
                <q-card>
                  <q-card-section> {{ t('bulkPage.faq.questions.payment.answer') }} </q-card-section>
                </q-card>
              </q-expansion-item>

              <q-expansion-item expand-separator icon="help_outline" :label="t('bulkPage.faq.questions.delivery.question')" header-class="text-primary">
                <q-card>
                  <q-card-section>
                    {{ t('bulkPage.faq.questions.delivery.answer') }}
                  </q-card-section>
                </q-card>
              </q-expansion-item>

              <q-expansion-item expand-separator icon="help_outline" :label="t('bulkPage.faq.questions.quality.question')" header-class="text-primary">
                <q-card>
                  <q-card-section>
                    {{ t('bulkPage.faq.questions.quality.answer') }}
                  </q-card-section>
                </q-card>
              </q-expansion-item>

              <q-expansion-item expand-separator icon="help_outline" :label="t('bulkPage.faq.questions.sample.question')" header-class="text-primary">
                <q-card>
                  <q-card-section>
                    {{ t('bulkPage.faq.questions.sample.answer') }}
                  </q-card-section>
                </q-card>
              </q-expansion-item>
            </q-list>
          </div>
        </div>
      </div>
    </div>

    <!-- 联系我们 -->
    <div id="contact" class="contact-section q-py-xl">
      <div class="container">
        <div class="text-center q-mb-lg">
          <h2 class="text-h5 text-weight-bold">{{ t('bulkPage.contact.title') }}</h2>
          <div class="separator"></div>
          <p class="q-mt-md">{{ t('bulkPage.contact.subtitle') }}</p>
        </div>

        <div class="row q-col-gutter-lg">
          <div class="col-12 col-md-6">
            <q-card class="contact-card">
              <q-card-section>
                <div class="text-center q-mb-md">
                  <q-icon name="contact_support" size="4rem" color="primary" />
                  <h3 class="text-h6 q-mt-sm">{{ t('bulkPage.contact.customerService.title') }}</h3>
                </div>

                <div class="row q-col-gutter-sm">
                  <div class="col-12">
                    <q-item>
                      <q-item-section avatar>
                        <q-icon color="primary" name="phone" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ t('bulkPage.contact.customerService.phone.label') }}</q-item-label>
                        <q-item-label caption>{{ t('bulkPage.contact.customerService.phone.value') }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </div>

                  <div class="col-12">
                    <q-item>
                      <q-item-section avatar>
                        <q-icon color="primary" name="email" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ t('bulkPage.contact.customerService.email.label') }}</q-item-label>
                        <q-item-label caption>{{ t('bulkPage.contact.customerService.email.value') }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </div>

                  <div class="col-12">
                    <q-item>
                      <q-item-section avatar>
                        <q-icon color="primary" name="chat" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ t('bulkPage.contact.customerService.online.label') }}</q-item-label>
                        <q-item-label caption>{{ t('bulkPage.contact.customerService.online.value') }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-md-6">
            <q-card class="contact-form-card">
              <q-card-section>
                <div class="text-center q-mb-md">
                  <q-icon name="edit_note" size="4rem" color="primary" />
                  <h3 class="text-h6 q-mt-sm">{{ t('bulkPage.contact.inquiry.title') }}</h3>
                </div>

                <div v-if="!submitSuccess">
                  <q-form @submit="onSubmit" class="q-gutter-md">
                    <q-input
                      v-model="form.name"
                      :label="t('bulkPage.contact.inquiry.form.name.label')"
                      :rules="[
                        (val) => !!val || t('bulkPage.contact.inquiry.form.name.required'),
                        (val) => val.length >= 2 || t('bulkPage.contact.inquiry.form.name.minLength'),
                        (val) => val.length <= 30 || t('bulkPage.contact.inquiry.form.name.maxLength'),
                      ]"
                      counter
                      maxlength="30"
                      :hint="t('bulkPage.contact.inquiry.form.name.placeholder')"
                      outlined
                      dense />

                    <q-input
                      v-model="form.email"
                      :label="t('bulkPage.contact.inquiry.form.email.label')"
                      type="email"
                      :rules="[
                        (val) => !!val || t('bulkPage.contact.inquiry.form.email.required'),
                        (val) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) || t('bulkPage.contact.inquiry.form.email.invalid'),
                        (val) => val.length <= 50 || t('bulkPage.contact.inquiry.form.email.maxLength'),
                      ]"
                      counter
                      maxlength="50"
                      :hint="t('bulkPage.contact.inquiry.form.email.placeholder')"
                      outlined
                      dense />

                    <q-input
                      v-model="form.companyName"
                      :label="t('bulkPage.contact.inquiry.form.companyName.label')"
                      :rules="[(val) => !val || val.length <= 50 || t('bulkPage.contact.inquiry.form.companyName.maxLength')]"
                      counter
                      maxlength="50"
                      :hint="t('bulkPage.contact.inquiry.form.companyName.placeholder')"
                      outlined
                      dense />

                    <q-input
                      v-model="form.phone"
                      :label="t('bulkPage.contact.inquiry.form.phone.label')"
                      :rules="[(val) => !val || /^[\d\s\+\-]{5,20}$/.test(val) || t('bulkPage.contact.inquiry.form.phone.invalid')]"
                      counter
                      maxlength="20"
                      :hint="t('bulkPage.contact.inquiry.form.phone.placeholder')"
                      outlined
                      dense />

                    <q-input
                      v-model="form.description"
                      :label="t('bulkPage.contact.inquiry.form.description.label')"
                      type="textarea"
                      :rules="[
                        (val) => !!val || t('bulkPage.contact.inquiry.form.description.required'),
                        (val) => val.length >= 10 || t('bulkPage.contact.inquiry.form.description.minLength'),
                        (val) => val.length <= 500 || t('bulkPage.contact.inquiry.form.description.maxLength'),
                      ]"
                      counter
                      maxlength="500"
                      :hint="t('bulkPage.contact.inquiry.form.description.placeholder')"
                      outlined
                      rows="4" />

                    <div class="text-center q-mt-lg">
                      <q-btn :label="t('bulkPage.contact.inquiry.form.submit')" type="submit" color="primary" :loading="submitting" icon-right="send" class="q-px-md" />
                    </div>
                  </q-form>
                </div>
                <div v-else class="success-message">
                  <div class="text-center q-py-xl">
                    <q-icon name="check_circle" color="positive" size="4rem" />
                    <h3 class="text-h5 q-mt-md text-positive">{{ t('bulkPage.contact.inquiry.success.title') }}</h3>
                    <p class="q-mt-md">{{ t('bulkPage.contact.inquiry.success.message') }}</p>
                    <q-btn :label="t('bulkPage.contact.inquiry.success.button')" color="primary" class="q-mt-lg" to="/" />
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </div>
  </div>

  <Footer />
  <!-- 侧边栏 -->
  <FixedBar />
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import InquiryApi from '~/composables/inquiryApi';

const $q = useQuasar();
const { t } = useI18n();

// 表单数据
const form = reactive({
  name: '',
  companyName: '',
  phone: '',
  email: '',
  description: '',
  type: 'bulk',
});

// 提交状态
const submitting = ref(false);
// 提交成功状态
const submitSuccess = ref(false);

// 提交表单
async function onSubmit() {
  submitting.value = true;

  const { code } = await InquiryApi.createInquiry(form);

  if (code === 0) {
    // 设置提交成功状态
    submitSuccess.value = true;

    // 显示成功提示
    $q.notify({
      color: 'positive',
      message: t('bulkPage.contact.inquiry.notification.success'),
      icon: 'check_circle',
      position: 'top',
      timeout: 3000,
    });

    // 重置表单
    Object.keys(form).forEach((key) => {
      if (key !== 'type') {
        // 保留表单类型
        form[key] = '';
      }
    });
  } else {
    // 显示错误提示
    $q.notify({
      color: 'negative',
      message: t('bulkPage.contact.inquiry.notification.error'),
      icon: 'error',
      position: 'top',
      timeout: 3000,
    });
  }

  // 无论成功还是失败，都重置提交状态
  submitting.value = false;
}

// 滚动到联系部分
function scrollToContact() {
  const element = document.getElementById('contact');
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
}
</script>

<style lang="scss" scoped>
.bulk-purchase-container {
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
  }

  // 顶部横幅
  .banner-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
    padding: 40px 0;
    border-radius: 8px;
    margin-bottom: 40px;

    h1 {
      color: #1976d2;
    }

    @media (max-width: 767px) {
      text-align: center;

      h1 {
        font-size: 1.8rem;
      }
    }
  }

  // 分隔线
  .separator {
    width: 60px;
    height: 3px;
    background-color: #1976d2;
    margin: 0 auto;
  }

  // 优势卡片
  .advantage-card {
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
  }

  // 场景卡片
  .scenario-card {
    height: 100%;
    overflow: hidden;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }

    .q-img {
      transition: transform 0.5s ease;
    }

    &:hover .q-img {
      transform: scale(1.05);
    }
  }

  // 服务流程
  .process-section {
    background-color: #f5f7fa;
  }

  .process-timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 20px;
      height: 100%;
      width: 2px;
      background-color: #1976d2;

      @media (min-width: 768px) {
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }

  .process-step {
    position: relative;
    margin-bottom: 30px;
    padding-left: 60px;

    @media (min-width: 768px) {
      padding-left: 0;

      &:nth-child(odd) {
        text-align: right;
        padding-right: 50%;
        padding-left: 0;

        .process-content {
          padding-right: 60px;
        }

        .process-icon {
          right: auto;
          left: 50%;
          margin-left: -20px;
        }
      }

      &:nth-child(even) {
        text-align: left;
        padding-left: 50%;

        .process-content {
          padding-left: 60px;
        }

        .process-icon {
          left: 50%;
          margin-left: -20px;
        }
      }
    }

    .process-icon {
      position: absolute;
      left: 0;
      top: 0;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #1976d2;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;
    }

    .process-content {
      background-color: white;
      padding: 15px;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
  }

  // 常见问题
  .faq-section {
    background-color: white;
  }

  // 联系我们
  .contact-section {
    background-color: #f5f7fa;
  }

  .contact-card,
  .contact-form-card {
    height: 100%;
  }

  // 成功提示样式
  .success-message {
    background-color: #f0f8f1;
    border-radius: 8px;
    padding: 20px;

    .q-icon {
      filter: drop-shadow(0 2px 5px rgba(0, 128, 0, 0.2));
    }
  }
}

// 响应式调整
@media (max-width: 599px) {
  .bulk-purchase-container {
    .banner-section {
      padding: 20px 0;
    }

    .process-step {
      margin-bottom: 20px;

      .process-content {
        padding: 10px;
      }
    }
  }
}
</style>
