<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" persistent>
    <q-card class="wallet-payment-dialog">
      <q-card-section class="wallet-payment-header">
        <div class="wallet-payment-title">
          <q-icon name="account_balance_wallet" size="24px" color="primary" class="q-mr-sm" />
          <span>{{ $t('payment.walletPay.title') }}</span>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section class="wallet-payment-content">
        <div class="wallet-payment-icon">
          <q-icon name="payment" size="48px" color="primary" />
        </div>

        <div class="wallet-payment-message">
          <p>{{ $t('payment.walletPay.confirmMessage') }}</p>
        </div>

        <div class="wallet-payment-amount">
          <span v-html="amount"></span>
        </div>

        <div class="wallet-payment-balance">
          <span>{{ $t('payment.walletPay.currentBalance') }}</span>
          <span v-html="balance" class="balance-value"></span>
        </div>

        <!-- 支付密码输入框 -->
        <div class="wallet-payment-password q-mt-md">
          <q-input
            v-model="payPassword"
            type="password"
            :label="$t('payment.walletPay.payPassword')"
            :placeholder="$t('payment.walletPay.payPasswordPlaceholder')"
            outlined
            dense
            maxlength="6"
            :rules="[
              (val) => !!val || $t('payment.walletPay.payPasswordRequired'),
              (val) => (val.length === 6 && /^\d+$/.test(val)) || $t('payment.walletPay.payPasswordInvalid')
            ]"
            class="payment-password-input"
            @keyup.enter="onOKClick">
            <template #prepend>
              <q-icon name="lock" color="primary" />
            </template>
          </q-input>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right" class="wallet-payment-actions">
        <q-btn flat :label="$t('common.cancel')" color="grey-7" @click="onCancelClick" />
        <q-btn unelevated :label="$t('payment.walletPay.confirm')" color="primary" @click="onOKClick" :disable="!isFormValid" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useDialogPluginComponent } from 'quasar';

const props = defineProps({
  // 支付金额，已格式化的HTML
  amount: {
    type: String,
    required: true,
  },
  // 当前余额，已格式化的HTML
  balance: {
    type: String,
    required: true,
  },
});

// 定义组件将发出的事件
defineEmits([
  // 必须包含这些事件
  ...useDialogPluginComponent.emits,
]);

// 使用Quasar的对话框插件组件
const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } = useDialogPluginComponent();

// 支付密码
const payPassword = ref('');

// 表单验证状态
const isFormValid = computed(() => {
  return payPassword.value.length === 6 && /^\d+$/.test(payPassword.value);
});

// 确认按钮点击事件
const onOKClick = () => {
  if (isFormValid.value) {
    onDialogOK(payPassword.value);
  }
};

// 取消按钮点击事件
const onCancelClick = () => {
  onDialogCancel();
};
</script>

<style lang="scss" scoped>
.wallet-payment-dialog {
  width: 400px;
  max-width: 90vw;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);

  @media (max-width: 599px) {
    width: 90vw;
  }
}

.wallet-payment-header {
  padding: 16px 20px;
  background-color: #f5f7fa;
}

.wallet-payment-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.wallet-payment-content {
  padding: 24px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.wallet-payment-icon {
  margin-bottom: 16px;
  background-color: rgba(25, 118, 210, 0.1);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wallet-payment-message {
  text-align: center;
  margin-bottom: 16px;
  color: #555;
  font-size: 15px;
}

.wallet-payment-amount {
  font-size: 24px;
  font-weight: 600;
  color: #f44336;
  margin-bottom: 16px;

  :deep(.primary-currency) {
    font-size: 1.1em;
  }
}

.wallet-payment-balance {
  font-size: 14px;
  color: #666;
  display: flex;
  flex-direction: column;
  align-items: center;

  .balance-value {
    margin-top: 4px;
    font-weight: 500;
    color: #333;
  }
}

.wallet-payment-password {
  width: 100%;
  max-width: 280px;
  margin: 0 auto;
}

.payment-password-input {
  :deep(.q-field__control) {
    border-radius: 8px;
  }

  :deep(.q-field__native) {
    text-align: center;
    font-weight: 500;
  }
}

.wallet-payment-actions {
  padding: 12px 20px;
  background-color: #f5f7fa;
}
</style>
