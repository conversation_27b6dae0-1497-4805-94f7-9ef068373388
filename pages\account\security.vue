<template>
  <div>
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-icon name="security" size="xs" color="primary" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('security.title') }}</span>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="q-pa-md">
      <!-- 密码管理卡片 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="row items-center q-mb-sm">
            <div class="text-subtitle1 text-weight-medium">
              <q-icon name="lock" color="primary" size="xs" class="q-mr-xs" />
              {{ $t('security.cards.passwordManagement.title') }}
            </div>
          </div>

          <!-- 登录密码 -->
          <q-list separator>
            <q-item>
              <q-item-section>
                <q-item-label>{{ $t('security.cards.passwordManagement.loginPassword.label') }}</q-item-label>
                <q-item-label caption>{{ $t('security.cards.passwordManagement.loginPassword.description') }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn flat color="primary" :label="$t('security.buttons.modify')" @click="showLoginPasswordDialog = true" />
              </q-item-section>
            </q-item>

            <!-- 支付密码 -->
            <q-item>
              <q-item-section>
                <q-item-label>{{ $t('security.cards.passwordManagement.paymentPassword.label') }}</q-item-label>
                <q-item-label caption>{{ $t('security.cards.passwordManagement.paymentPassword.description') }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn flat color="primary" :label="hasPayPassword ? $t('security.buttons.modify') : $t('security.buttons.set')" @click="showPayPasswordDialog = true" />
              </q-item-section>
            </q-item>
            <!-- 补款授权 -->
            <q-item>
              <q-item-section>
                <q-item-label>{{ $t('security.cards.passwordManagement.autoPayment.label') }}</q-item-label>
                <q-item-label caption>{{ $t('security.cards.passwordManagement.autoPayment.description') }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-toggle v-model="securitySettings.autoPayment" color="primary" @update:model-value="updateAutoPayment" />
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>
      </q-card>

      <!-- 账号绑定卡片 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="row items-center q-mb-sm">
            <div class="text-subtitle1 text-weight-medium">
              <q-icon name="link" color="primary" size="xs" class="q-mr-xs" />
              {{ $t('security.cards.accountBinding.title') }}
            </div>
          </div>

          <q-list separator>
            <!-- 邮箱绑定 -->
            <q-item>
              <q-item-section avatar>
                <q-avatar color="blue-grey" text-color="white" icon="email" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ $t('security.cards.accountBinding.email.label') }}</q-item-label>
                <q-item-label caption>
                  {{ userInfo.email }}
                  <q-badge v-if="userInfo.status === 1" color="positive" class="q-ml-sm">{{ $t('security.cards.accountBinding.email.verified') }}</q-badge>
                  <q-badge v-else color="negative" class="q-ml-sm">{{ $t('security.cards.accountBinding.email.unverified') }}</q-badge>
                </q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn v-if="userInfo.status !== 1" flat color="primary" :label="$t('security.buttons.verify')" @click="sendVerifyEmail" />
              </q-item-section>
            </q-item>

            <!-- 手机绑定 -->
            <q-item>
              <q-item-section avatar>
                <q-avatar color="deep-orange" text-color="white" icon="phone_android" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ $t('security.cards.accountBinding.mobile.label') }}</q-item-label>
                <q-item-label caption>
                  {{ userInfo.mobile || $t('security.cards.accountBinding.mobile.notBound') }}
                </q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn flat color="primary" :label="userInfo.mobile ? $t('security.buttons.modify') : $t('security.buttons.bind')" @click="goToProfile" />
              </q-item-section>
            </q-item>

            <!-- 微信绑定 -->
            <q-item>
              <q-item-section avatar>
                <q-avatar color="green" text-color="white">
                  <q-icon name="img:https://cdn.quasar.dev/logo-v2/svg/logo.svg" />
                </q-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ $t('security.cards.accountBinding.wechat.label') }}</q-item-label>
                <q-item-label caption>
                  {{ socialAccounts.wechat ? $t('security.cards.accountBinding.status.bound') : $t('security.cards.accountBinding.status.notBound') }}
                </q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn v-if="!socialAccounts.wechat" flat color="primary" :label="$t('security.buttons.bind')" @click="bindSocialAccount('wechat')" />
                <q-btn v-else flat color="negative" :label="$t('security.buttons.unbind')" @click="unbindSocialAccount('wechat')" />
              </q-item-section>
            </q-item>

            <!-- 谷歌绑定 -->
            <q-item>
              <q-item-section avatar>
                <q-avatar color="red" text-color="white">
                  <q-icon name="img:https://cdn.quasar.dev/logo-v2/svg/logo.svg" />
                </q-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ $t('security.cards.accountBinding.google.label') }}</q-item-label>
                <q-item-label caption>
                  {{ socialAccounts.google ? $t('security.cards.accountBinding.status.bound') : $t('security.cards.accountBinding.status.notBound') }}
                </q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn v-if="!socialAccounts.google" flat color="primary" :label="$t('security.buttons.bind')" @click="bindSocialAccount('google')" />
                <q-btn v-else flat color="negative" :label="$t('security.buttons.unbind')" @click="unbindSocialAccount('google')" />
              </q-item-section>
            </q-item>

            <!-- Facebook绑定 -->
            <q-item>
              <q-item-section avatar>
                <q-avatar color="blue" text-color="white">
                  <q-icon name="img:https://cdn.quasar.dev/logo-v2/svg/logo.svg" />
                </q-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ $t('security.cards.accountBinding.facebook.label') }}</q-item-label>
                <q-item-label caption>
                  {{ socialAccounts.facebook ? $t('security.cards.accountBinding.status.bound') : $t('security.cards.accountBinding.status.notBound') }}
                </q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn v-if="!socialAccounts.facebook" flat color="primary" :label="$t('security.buttons.bind')" @click="bindSocialAccount('facebook')" />
                <q-btn v-else flat color="negative" :label="$t('security.buttons.unbind')" @click="unbindSocialAccount('facebook')" />
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>
      </q-card>

      <!-- 安全设置卡片 -->
      <q-card flat bordered>
        <q-card-section>
          <div class="row items-center q-mb-sm">
            <div class="text-subtitle1 text-weight-medium">
              <q-icon name="shield" color="primary" size="xs" class="q-mr-xs" />
              安全设置
            </div>
          </div>

          <q-list separator>
            <!-- 登录设备管理 -->
            <q-item>
              <q-item-section>
                <q-item-label>登录设备管理</q-item-label>
                <q-item-label caption>查看并管理您的登录设备</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn flat color="primary" label="查看" @click="showDeviceDialog = true" />
              </q-item-section>
            </q-item>

            <!-- 登录通知 -->
            <q-item>
              <q-item-section>
                <q-item-label>登录通知</q-item-label>
                <q-item-label caption>异地登录时通过邮件通知</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-toggle v-model="securitySettings.loginNotify" color="primary" />
              </q-item-section>
            </q-item>

            <!-- 两步验证 -->
            <q-item>
              <q-item-section>
                <q-item-label>两步验证</q-item-label>
                <q-item-label caption>登录时需要额外验证码</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-toggle v-model="securitySettings.twoFactorAuth" color="primary" />
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>
      </q-card>
    </div>

    <!-- 修改登录密码弹窗 -->
    <q-dialog v-model="showLoginPasswordDialog" persistent>
      <q-card style="min-width: 350px; max-width: 400px">
        <q-card-section class="row items-center">
          <div class="text-h6">{{ $t('security.dialogs.loginPassword.title') }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <q-card-section>
          <q-form ref="loginPasswordFormRef" @submit="updateLoginPassword">
            <q-input
              v-model="loginPasswordForm.oldPassword"
              :label="$t('security.dialogs.loginPassword.fields.currentPassword')"
              type="password"
              :rules="[(val) => !!val || $t('security.dialogs.loginPassword.validations.currentPasswordRequired')]"
              outlined
              dense
              class="q-mb-md" />
            <q-input
              v-model="loginPasswordForm.newPassword"
              :label="$t('security.dialogs.loginPassword.fields.newPassword')"
              type="password"
              :rules="[
                (val) => !!val || $t('security.dialogs.loginPassword.validations.newPasswordRequired'),
                (val) => val.length >= 6 || $t('security.dialogs.loginPassword.validations.passwordLength'),
                (val) => val !== loginPasswordForm.oldPassword || $t('security.dialogs.loginPassword.validations.passwordDifferent'),
              ]"
              outlined
              dense
              class="q-mb-md" />
            <q-input
              v-model="loginPasswordForm.confirmPassword"
              :label="$t('security.dialogs.loginPassword.fields.confirmPassword')"
              type="password"
              :rules="[
                (val) => !!val || $t('security.dialogs.loginPassword.validations.confirmPasswordRequired'),
                (val) => val === loginPasswordForm.newPassword || $t('security.dialogs.loginPassword.validations.passwordMatch'),
              ]"
              outlined
              dense
              class="q-mb-md" />
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="$t('security.buttons.cancel')" color="grey-7" v-close-popup />
          <q-btn flat :label="$t('security.buttons.confirm')" color="primary" @click="updateLoginPassword" :loading="updatingLoginPassword" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 设置/修改支付密码弹窗 -->
    <q-dialog v-model="showPayPasswordDialog" persistent>
      <q-card style="min-width: 350px; max-width: 400px">
        <q-card-section class="row items-center">
          <div class="text-h6">{{ hasPayPassword ? $t('security.dialogs.paymentPassword.titleModify') : $t('security.dialogs.paymentPassword.titleSet') }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <q-card-section>
          <q-form ref="payPasswordFormRef" @submit="updatePayPassword">
            <q-input
              v-if="hasPayPassword"
              v-model="payPasswordForm.oldPassword"
              label="当前支付密码"
              type="password"
              :rules="[(val) => !!val || '请输入当前支付密码']"
              outlined
              dense
              class="q-mb-md" />
            <q-input
              v-model="payPasswordForm.newPassword"
              label="新支付密码"
              type="password"
              :rules="[
                (val) => !!val || '请输入新支付密码',
                (val) => (val.length === 6 && /^\d+$/.test(val)) || '支付密码必须为6位数字',
                (val) => !hasPayPassword || val !== payPasswordForm.oldPassword || '新支付密码不能与当前支付密码相同',
              ]"
              outlined
              dense
              class="q-mb-md"
              maxlength="6" />
            <q-input
              v-model="payPasswordForm.confirmPassword"
              label="确认新支付密码"
              type="password"
              :rules="[(val) => !!val || '请确认新支付密码', (val) => val === payPasswordForm.newPassword || '两次输入的支付密码不一致']"
              outlined
              dense
              class="q-mb-md"
              maxlength="6" />
            <div class="text-caption text-grey-7 q-mb-md">支付密码用于账户余额支付和提现等操作，请设置6位数字密码。</div>
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey-7" v-close-popup />
          <q-btn flat label="确认" color="primary" @click="updatePayPassword" :loading="updatingPayPassword" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 登录设备管理弹窗 -->
    <q-dialog v-model="showDeviceDialog" persistent>
      <q-card style="min-width: 350px; max-width: 600px">
        <q-card-section class="row items-center">
          <div class="text-h6">登录设备管理</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <q-card-section style="max-height: 400px" class="scroll">
          <q-list separator>
            <q-item v-for="(device, index) in loginDevices" :key="index">
              <q-item-section avatar>
                <q-avatar :color="device.current ? 'primary' : 'grey'" text-color="white">
                  <q-icon :name="getDeviceIcon(device.deviceType)" />
                </q-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ device.deviceName }}</q-item-label>
                <q-item-label caption>
                  {{ device.location }} · {{ device.lastLoginTime }}
                  <q-badge v-if="device.current" color="primary" class="q-ml-sm">当前设备</q-badge>
                </q-item-label>
              </q-item-section>
              <q-item-section side v-if="!device.current">
                <q-btn flat round dense color="negative" icon="logout" @click="logoutDevice(device.id)">
                  <q-tooltip>退出登录</q-tooltip>
                </q-btn>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="关闭" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useUserStore } from '~/store/user';
import UserApi from '~/composables/userApi';
import AuthApi from '~/composables/authApi';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const $q = useQuasar();
const router = useRouter();
const userStore = useUserStore();
const { t } = useI18n();

// 用户信息
const userInfo = ref({
  name: '',
  nickname: '',
  gender: 0,
  mobile: '',
  email: '',
  avatar: '',
  status: 0, // 邮箱验证状态
  hasPassword: false, // 是否设置了密码
  hasPayPassword: false, // 是否设置了支付密码
  autoCompensateDiff: false, // 是否自动补款
});

// 社交账号绑定状态
const socialAccounts = reactive({
  wechat: false,
  google: false,
  facebook: false,
});

// 安全设置
const securitySettings = reactive({
  loginNotify: true,
  twoFactorAuth: false,
  autoPayment: false, // 补款授权
});

// 支付密码状态
const hasPayPassword = ref(false);

// 登录设备列表
const loginDevices = ref([
  {
    id: 1,
    deviceName: 'Chrome on Windows 10',
    deviceType: 'desktop',
    location: '中国 上海',
    lastLoginTime: '2023-04-28 14:30:25',
    current: true,
  },
  {
    id: 2,
    deviceName: 'Safari on iPhone 13',
    deviceType: 'mobile',
    location: '中国 北京',
    lastLoginTime: '2023-04-25 09:15:10',
    current: false,
  },
  {
    id: 3,
    deviceName: 'Firefox on macOS',
    deviceType: 'desktop',
    location: '中国 广州',
    lastLoginTime: '2023-04-20 18:45:33',
    current: false,
  },
]);

// 弹窗控制
const showLoginPasswordDialog = ref(false);
const showPayPasswordDialog = ref(false);
const showDeviceDialog = ref(false);

// 表单引用
const loginPasswordFormRef = ref(null);
const payPasswordFormRef = ref(null);

// 表单数据
const loginPasswordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
});

const payPasswordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
});

// 加载状态
const updatingLoginPassword = ref(false);
const updatingPayPassword = ref(false);

// 页面加载时获取用户信息
onMounted(async () => {
  await getUserInfo();
  // 模拟获取社交账号绑定状态
  socialAccounts.wechat = Math.random() > 0.5;
  socialAccounts.google = Math.random() > 0.5;
  socialAccounts.facebook = Math.random() > 0.5;
  // 模拟获取支付密码状态
  hasPayPassword.value = Math.random() > 0.5;
});

// 获取用户信息
async function getUserInfo() {
  try {
    await userStore.getUserInfo();
    userInfo.value = userStore.userInfo;
    console.log('用户信息', userInfo.value);
  } catch (error) {
    console.error('获取用户信息失败', error);
    $q.notify({
      color: 'negative',
      message: '获取用户信息失败，请刷新页面重试',
      icon: 'error',
    });
  }
}

// 发送邮箱验证邮件
async function sendVerifyEmail() {
  try {
    const { code, msg } = await AuthApi.resend({
      email: userInfo.value.email,
    });

    if (code === 0) {
      $q.notify({
        color: 'positive',
        message: '验证邮件已发送，请查收',
        icon: 'check',
      });
    } else {
      $q.notify({
        color: 'negative',
        message: msg || '发送验证邮件失败',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('发送验证邮件失败', error);
    $q.notify({
      color: 'negative',
      message: '发送验证邮件失败，请重试',
      icon: 'error',
    });
  }
}

// 跳转到个人信息页面
function goToProfile() {
  router.push('/account/profile');
}

// 更新登录密码
async function updateLoginPassword() {
  try {
    // 表单验证
    const isValid = await loginPasswordFormRef.value.validate();
    if (!isValid) return;

    updatingLoginPassword.value = true;

    // 调用API更新登录密码
    const { code, msg } = await UserApi.updateUserPassword({
      oldPassword: loginPasswordForm.oldPassword,
      newPassword: loginPasswordForm.newPassword,
    });

    if (code === 0) {
      $q.notify({
        color: 'positive',
        message: '登录密码修改成功',
        icon: 'check',
      });
      showLoginPasswordDialog.value = false;
      // 重置表单
      loginPasswordForm.oldPassword = '';
      loginPasswordForm.newPassword = '';
      loginPasswordForm.confirmPassword = '';
    } else {
      $q.notify({
        color: 'negative',
        message: msg || '密码修改失败，请重试',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('修改登录密码失败', error);
    $q.notify({
      color: 'negative',
      message: '修改失败，请重试',
      icon: 'error',
    });
  } finally {
    updatingLoginPassword.value = false;
  }
}

// 更新支付密码
async function updatePayPassword() {
  try {
    // 表单验证
    const isValid = await payPasswordFormRef.value.validate();
    if (!isValid) return;

    updatingPayPassword.value = true;

    // 这里应该调用实际的API，目前模拟成功
    // const { code, msg } = await PayApi.updatePayPassword({
    //   oldPassword: hasPayPassword.value ? payPasswordForm.oldPassword : undefined,
    //   newPassword: payPasswordForm.newPassword,
    // });

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));
    const code = 0;

    if (code === 0) {
      $q.notify({
        color: 'positive',
        message: hasPayPassword.value ? '支付密码修改成功' : '支付密码设置成功',
        icon: 'check',
      });
      showPayPasswordDialog.value = false;
      hasPayPassword.value = true;
      // 重置表单
      payPasswordForm.oldPassword = '';
      payPasswordForm.newPassword = '';
      payPasswordForm.confirmPassword = '';
    } else {
      $q.notify({
        color: 'negative',
        message: '操作失败，请重试',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('支付密码操作失败', error);
    $q.notify({
      color: 'negative',
      message: '操作失败，请重试',
      icon: 'error',
    });
  } finally {
    updatingPayPassword.value = false;
  }
}
// 更新补款授权设置
async function updateAutoPayment() {
  try {
    // 这里应该调用实际的API，目前模拟成功
    // const { code, msg } = await UserApi.updateAutoPayment(securitySettings.autoPayment);

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500));
    const code = 0;

    if (code === 0) {
      $q.notify({
        color: 'positive',
        message: securitySettings.autoPayment ? '已开启补款授权' : '已关闭补款授权',
        icon: 'check',
      });
    } else {
      // 如果API调用失败，恢复原来的状态
      securitySettings.autoPayment = !securitySettings.autoPayment;
      $q.notify({
        color: 'negative',
        message: '设置失败，请重试',
        icon: 'error',
      });
    }
  } catch (error) {
    // 如果发生异常，恢复原来的状态
    securitySettings.autoPayment = !securitySettings.autoPayment;
    console.error('更新补款授权设置失败', error);
    $q.notify({
      color: 'negative',
      message: '设置失败，请重试',
      icon: 'error',
    });
  }
}

// 更新安全设置
async function updateSecuritySetting(setting) {
  try {
    // 这里应该调用实际的API，目前模拟成功
    // const { code, msg } = await UserApi.updateSecuritySetting(setting, securitySettings[setting]);

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500));
    const code = 0;

    if (code === 0) {
      const settingNames = {
        loginNotify: '登录通知',
        twoFactorAuth: '两步验证',
      };

      $q.notify({
        color: 'positive',
        message: `已${securitySettings[setting] ? '开启' : '关闭'}${settingNames[setting]}`,
        icon: 'check',
      });
    } else {
      // 如果API调用失败，恢复原来的状态
      securitySettings[setting] = !securitySettings[setting];
      $q.notify({
        color: 'negative',
        message: '设置失败，请重试',
        icon: 'error',
      });
    }
  } catch (error) {
    // 如果发生异常，恢复原来的状态
    securitySettings[setting] = !securitySettings[setting];
    console.error('更新安全设置失败', error);
    $q.notify({
      color: 'negative',
      message: '设置失败，请重试',
      icon: 'error',
    });
  }
}

// 绑定社交账号
function bindSocialAccount(type) {
  // 这里应该调用实际的API，目前模拟成功
  $q.notify({
    color: 'info',
    message: `正在跳转到${type}授权页面...`,
    icon: 'info',
  });

  // 模拟绑定成功
  setTimeout(() => {
    socialAccounts[type] = true;
    $q.notify({
      color: 'positive',
      message: `${type}账号绑定成功`,
      icon: 'check',
    });
  }, 2000);
}

// 解绑社交账号
function unbindSocialAccount(type) {
  $q.dialog({
    title: '确认解绑',
    message: `确定要解绑${type}账号吗？`,
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    // 这里应该调用实际的API，目前模拟成功
    // const { code, msg } = await AuthApi.unbindSocialAccount(type);

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));
    const code = 0;

    if (code === 0) {
      socialAccounts[type] = false;
      $q.notify({
        color: 'positive',
        message: `${type}账号解绑成功`,
        icon: 'check',
      });
    } else {
      $q.notify({
        color: 'negative',
        message: '解绑失败，请重试',
        icon: 'error',
      });
    }
  });
}

// 退出设备登录
function logoutDevice(deviceId) {
  $q.dialog({
    title: '确认退出',
    message: '确定要退出该设备的登录状态吗？',
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    // 这里应该调用实际的API，目前模拟成功
    // const { code, msg } = await AuthApi.logoutDevice(deviceId);

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));
    const code = 0;

    if (code === 0) {
      // 从设备列表中移除
      loginDevices.value = loginDevices.value.filter((device) => device.id !== deviceId);
      $q.notify({
        color: 'positive',
        message: '设备已退出登录',
        icon: 'check',
      });
    } else {
      $q.notify({
        color: 'negative',
        message: '操作失败，请重试',
        icon: 'error',
      });
    }
  });
}

// 获取设备图标
function getDeviceIcon(deviceType) {
  switch (deviceType) {
    case 'mobile':
      return 'smartphone';
    case 'tablet':
      return 'tablet';
    case 'desktop':
    default:
      return 'computer';
  }
}
</script>

<style lang="scss" scoped>
.q-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .q-card-section {
    padding: 16px;
  }

  .text-subtitle1 {
    font-size: 1rem;
    font-weight: 500;
  }
}

.q-item {
  min-height: 48px;
  padding: 8px 16px;

  .q-item__section--avatar {
    min-width: 40px;
  }

  .q-item__label {
    font-size: 0.95rem;
  }

  .q-item__label--caption {
    font-size: 0.8rem;
  }
}

// 移动端优化
@media (max-width: 599px) {
  .q-card-section {
    padding: 12px;
  }

  .q-item {
    padding: 8px 12px;
    min-height: 40px;
  }

  .q-item__label {
    font-size: 0.9rem;
  }

  .q-item__label--caption {
    font-size: 0.75rem;
  }
}
</style>
