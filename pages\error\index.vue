<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-code">{{ errorCode }}</div>
      <h1 class="error-title">{{ errorTitle }}</h1>
      <p class="error-description">{{ errorDescription }}</p>

      <div class="error-image">
        <img :src="errorImage" :alt="`${errorCode} Error`" />
      </div>

      <div class="error-actions">
        <q-btn color="primary" icon="home" :label="$t('error.page.backToHome')" to="/" class="q-mr-md" />
        <q-btn outline color="primary" icon="arrow_back" :label="$t('error.page.goBack')" @click="goBack" />
      </div>

      <div class="error-help">
        <h3 class="help-title">{{ $t('error.page.needHelp') }}</h3>
        <p class="help-text">{{ $t('error.page.contactSupport') }}</p>
        <div class="help-contacts">
          <div class="contact-item">
            <q-icon name="phone" color="primary" size="sm" class="q-mr-xs" />
            <span>************</span>
          </div>
          <div class="contact-item">
            <q-icon name="mail" color="primary" size="sm" class="q-mr-xs" />
            <span><EMAIL></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';

const route = useRoute();
const router = useRouter();
const { t } = useI18n();

// 错误代码
const errorCode = ref(t('error.page.errorCode'));

// 根据错误代码设置标题和描述
const errorTitle = computed(() => {
  switch (errorCode.value) {
    case '400':
      return t('error.page.titles.400');
    case '401':
      return t('error.page.titles.401');
    case '403':
      return t('error.page.titles.403');
    case '404':
      return t('error.page.titles.404');
    case '500':
      return t('error.page.titles.500');
    case '503':
      return t('error.page.titles.503');
    default:
      return t('error.page.titles.default');
  }
});

// 错误描述
const errorDescription = computed(() => {
  switch (errorCode.value) {
    case '400':
      return t('error.page.descriptions.400');
    case '401':
      return t('error.page.descriptions.401');
    case '403':
      return t('error.page.descriptions.403');
    case '404':
      return t('error.page.descriptions.404');
    case '500':
      return t('error.page.descriptions.500');
    case '503':
      return t('error.page.descriptions.503');
    default:
      return t('error.page.descriptions.default');
  }
});

// 错误图片
const errorImage = computed(() => {
  switch (errorCode.value) {
    case '400':
      return '/images/error/400.svg';
    case '401':
      return '/images/error/401.svg';
    case '403':
      return '/images/error/403.svg';
    case '404':
      return '/images/error/404.svg';
    case '500':
      return '/images/error/500.svg';
    case '503':
      return '/images/error/503.svg';
    default:
      return '/images/error/error.svg';
  }
});

// 返回上一页
const goBack = () => {
  router.back();
};

// 从路由参数获取错误代码
onMounted(() => {
  if (route.query.code) {
    errorCode.value = route.query.code;
  }
});
</script>

<style lang="scss" scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  padding: 20px;
}

.error-container {
  max-width: 800px;
  width: 100%;
  text-align: center;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  padding: 40px 20px;
}

.error-code {
  font-size: 120px;
  font-weight: 900;
  color: #1976d2;
  line-height: 1;
  margin-bottom: 16px;
  text-shadow: 2px 2px 4px rgba(25, 118, 210, 0.2);
}

.error-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.error-description {
  font-size: 18px;
  color: #666;
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.error-image {
  max-width: 400px;
  margin: 0 auto 40px;

  img {
    width: 100%;
    height: auto;
  }
}

.error-actions {
  margin-bottom: 40px;
}

.error-help {
  max-width: 500px;
  margin: 0 auto;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.help-title {
  font-size: 18px;
  color: #333;
  margin-bottom: 8px;
}

.help-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 16px;
}

.help-contacts {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.contact-item {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #1976d2;
}

@media (max-width: 767px) {
  .error-code {
    font-size: 80px;
  }

  .error-title {
    font-size: 24px;
  }

  .error-description {
    font-size: 16px;
  }

  .error-image {
    max-width: 280px;
  }

  .help-title {
    font-size: 16px;
  }

  .help-text {
    font-size: 14px;
  }

  .contact-item {
    font-size: 14px;
  }
}
</style>
