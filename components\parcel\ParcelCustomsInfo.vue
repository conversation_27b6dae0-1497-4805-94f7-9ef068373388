<template>
  <div class="customs-info-section">
    <div class="row items-center q-mb-sm">
      <q-icon name="assignment" color="primary" size="sm" class="q-mr-sm" />
      <div class="text-h6">报关信息</div>
    </div>
    <div class="text-caption text-grey-8 q-mb-md">请填写包裹的报关信息，以便海关处理</div>

    <q-card flat bordered class="q-pa-md">
      <div class="row q-col-gutter-md">
        <!-- 申报价值 -->
        <div class="col-12 col-sm-6">
          <q-input
            v-model.number="localValue.declaredValue"
            type="number"
            label="申报价值 (USD)"
            outlined
            dense
            :rules="[(val) => (val !== null && val !== undefined) || '请输入申报价值', (val) => val >= 0 || '申报价值不能为负数']">
            <template #prepend>
              <q-icon name="attach_money" />
            </template>
          </q-input>
        </div>

        <!-- 收件人清关代码 -->
        <div class="col-12 col-sm-6">
          <q-input v-model="localValue.clearanceCode" label="收件人清关代码" outlined dense hint="如有清关代码请填写，没有可留空" />
        </div>

        <!-- 申报内容 -->
        <div class="col-12">
          <q-input v-model="localValue.declareContent" type="textarea" label="申报内容" outlined dense hint="请简要描述包裹内物品，例如：服装、电子产品等" counter maxlength="200" autogrow />
        </div>
      </div>

      <!-- 申报提示 -->
      <div class="text-caption text-grey-8 q-mt-md">
        <q-icon name="info" size="xs" class="q-mr-xs" />
        提示：准确的申报信息有助于包裹顺利通过海关检查，避免延误或退运。
      </div>
    </q-card>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

// 定义组件接收的属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      declaredValue: 0,
      clearanceCode: '',
      declareContent: '',
    }),
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update:modelValue']);

// 本地值，用于双向绑定
const localValue = ref({
  declaredValue: props.modelValue?.declaredValue || 0,
  clearanceCode: props.modelValue?.clearanceCode || '',
  declareContent: props.modelValue?.declareContent || '',
});

// 监听本地值变化，向父组件发送更新事件
watch(
  localValue,
  (newValue) => {
    emit('update:modelValue', {
      declaredValue: newValue.declaredValue || 0,
      clearanceCode: newValue.clearanceCode || '',
      declareContent: newValue.declareContent || '',
    });
  },
  { deep: true }
);

// 监听父组件传入的值变化，更新本地值
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      localValue.value = {
        declaredValue: newValue.declaredValue || 0,
        clearanceCode: newValue.clearanceCode || '',
        declareContent: newValue.declareContent || '',
      };
    }
  },
  { deep: true, immediate: true }
);
</script>

<style lang="scss" scoped>
.customs-info-section {
  .q-card {
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
