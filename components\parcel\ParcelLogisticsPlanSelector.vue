<template>
  <div class="shipping-selector-section">
    <div class="row items-center q-mb-sm">
      <q-icon name="local_shipping" color="primary" size="sm" class="q-mr-sm" />
      <div class="text-h6">物流方案</div>
    </div>
    <div class="text-caption text-grey-8 q-mb-md">请选择适合您的物流方案</div>

    <q-card flat bordered class="q-pa-md">
      <div class="row items-center">
        <div v-if="modelValue" class="col-grow">
          <div class="row items-center justify-between">
            <div>
              <div class="text-body1 text-weight-medium">
                {{ modelValue.name }}
                <q-badge color="green" class="q-ml-sm">推荐</q-badge>
              </div>
              <div class="text-body2">预计送达: {{ modelValue.transitTime }}</div>
              <div class="text-caption text-grey-8">{{ modelValue.description }}</div>
            </div>
            <div class="text-right">
              <div class="text-body1 text-primary text-weight-medium">¥{{ calculateShippingFee(modelValue) }}</div>
              <q-btn flat round color="primary" icon="edit" @click="openDialog" />
            </div>
          </div>
        </div>
        <div v-else class="col-grow text-center">
          <q-btn color="primary" label="选择物流方案" @click="openDialog" />
        </div>
      </div>
    </q-card>

    <!-- 物流方案选择弹窗 -->
    <q-dialog v-model="shippingDialog" persistent>
      <q-card style="width: 800px; max-width: 95vw" class="shipping-dialog">
        <q-card-section class="dialog-header row items-center justify-between">
          <div class="text-h6 text-weight-bold">选择物流方案</div>
          <q-btn
            icon="close"
            flat
            round
            dense
            v-close-popup
            color="white"
            class="close-btn"
          />
        </q-card-section>

        <q-card-section class="q-pt-none">
          <div class="text-body1 q-mb-md">
            包裹总重量: <span class="text-weight-medium">{{ parcelWeight }} g</span>
          </div>

          <!-- 物流方案列表 - 按价格排序 -->
          <div class="shipping-methods-container">
            <div
              v-for="shipping in sortedShippingMethods"
              :key="`${shipping.productId}-${shipping.priceId}`"
              @click="selectShipping(shipping)"
              class="shipping-method-item"
              :class="{ 'selected': isSelected(shipping) }">

              <!-- 选中状态指示器 -->
              <div class="selection-radio">
                <q-icon
                  :name="isSelected(shipping) ? 'radio_button_checked' : 'radio_button_unchecked'"
                  :color="isSelected(shipping) ? 'primary' : 'grey-5'"
                  size="sm"
                />
              </div>

              <!-- 方案信息 -->
              <div class="shipping-content">
                <div class="shipping-main">
                  <div class="shipping-title">
                    {{ shipping.name }}
                    <q-badge v-if="shipping.recommended" color="green" text-color="white" class="recommend-badge">推荐</q-badge>
                  </div>
                  <div class="shipping-price">¥{{ calculateShippingFee(shipping) }}</div>
                </div>

                <div class="shipping-meta">
                  <div class="delivery-info">
                    <q-icon name="schedule" size="xs" />
                    <span>预计送达: {{ shipping.transitTime || shipping.estimatedDelivery }}</span>
                  </div>

                  <!-- 特色服务标签 -->
                  <div class="service-tags" v-if="shipping.taxInclude || shipping.freeInsure">
                    <span v-if="shipping.taxInclude" class="service-tag tax-included">包税</span>
                    <span v-if="shipping.freeInsure" class="service-tag free-insurance">免费保险</span>
                  </div>
                </div>
              </div>
            </div>

            <div v-if="sortedShippingMethods.length === 0" class="text-center q-py-lg">
              <q-icon name="info" size="2rem" color="grey-7" />
              <div class="text-grey-7 q-mt-sm">暂无可用的物流方案</div>
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn flat label="确认" color="primary" :disable="!tempSelectedShipping" @click="confirmShipping" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, watch, computed, toRaw } from 'vue';
import { fen2yuan } from '../../utils/utils';

// 定义组件接收的属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  shippingMethods: {
    type: Array,
    required: true,
    default: () => [],
  },
  parcelWeight: {
    type: [Number, String],
    required: true,
    default: '0.00',
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update:modelValue', 'calculate-fee']);

// 弹窗状态
const shippingDialog = ref(false);
const tempSelectedShipping = ref(props.modelValue);

// 按价格排序的物流方案
const sortedShippingMethods = computed(() => {
  if (!props.shippingMethods || props.shippingMethods.length === 0) {
    return [];
  }

  return [...props.shippingMethods].sort((a, b) => {
    const feeA = parseFloat(a.totalFee || calculateShippingFee(a));
    const feeB = parseFloat(b.totalFee || calculateShippingFee(b));
    return feeA - feeB;
  });
});

// 计算物流费用
function calculateShippingFee(shipping) {
  if (!shipping) return '0.00';

  // 如果有 totalFee 字段，直接使用
  if (shipping.totalFee !== undefined && shipping.totalFee !== null) {
    return parseFloat(shipping.totalFee).toFixed(2);
  }

  // 否则按原来的方式计算
  const weight = parseFloat(props.parcelWeight);
  const fee = weight * 0.001 * fen2yuan(shipping.basePrice);
  return fee.toFixed(2);
}

// 打开物流方案选择弹窗
function openDialog() {
  tempSelectedShipping.value = props.modelValue;
  shippingDialog.value = true;
}

// 检查是否选中
function isSelected(shipping) {
  if (!tempSelectedShipping.value || !shipping) return false;

  // 获取原始值，避免响应式对象比较问题
  const selected = toRaw(tempSelectedShipping.value);
  const current = toRaw(shipping);

  // 根据数据结构，使用 productId 和 priceId 来判断
  const result = selected.productId === current.productId &&
                 selected.priceId === current.priceId;

  console.log('检查选中状态:', {
    shipping: current.name,
    selectedProductId: selected.productId,
    currentProductId: current.productId,
    selectedPriceId: selected.priceId,
    currentPriceId: current.priceId,
    isSelected: result
  });

  return result;
}

// 选择物流方案
function selectShipping(shipping) {
  tempSelectedShipping.value = shipping;
}

// 确认物流方案选择
function confirmShipping() {
  emit('update:modelValue', tempSelectedShipping.value);
  shippingDialog.value = false;
}

// 监听父组件传入的值变化
watch(
  () => props.modelValue,
  (newValue) => {
    tempSelectedShipping.value = newValue;
  }
);
</script>

<style lang="scss" scoped>
.shipping-selector-section {
  .q-card {
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }
}

.shipping-dialog {
  .dialog-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    border-radius: 8px 8px 0 0;

    .text-h6 {
      color: white;
      margin: 0;
      font-size: 1.1rem;
    }

    .close-btn {
      color: white;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }
}

.shipping-methods-container {
  max-height: 60vh;
  overflow-y: auto;
  padding: 0 4px;
}

.shipping-method-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
  display: flex;
  align-items: flex-start;
  gap: 12px;

  &:hover {
    border-color: #3b82f6;
    background: #f8fafc;
  }

  &.selected {
    border-color: #3b82f6;
    background: #eff6ff;
    box-shadow: 0 1px 3px rgba(59, 130, 246, 0.1);
  }
}

.selection-radio {
  flex-shrink: 0;
  margin-top: 2px;
}

.shipping-content {
  flex: 1;
  min-width: 0;
}

.shipping-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
}

.shipping-title {
  font-weight: 500;
  font-size: 15px;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.recommend-badge {
  font-size: 10px;
  padding: 2px 6px;
}

.shipping-price {
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
  white-space: nowrap;
}

.shipping-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.delivery-info {
  color: #6b7280;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.service-tags {
  display: flex;
  gap: 4px;
}

.service-tag {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.service-tag.tax-included {
  background: #dcfce7;
  color: #166534;
}

.service-tag.free-insurance {
  background: #dbeafe;
  color: #1e40af;
}

/* 弹窗样式优化 */
.q-dialog .q-card {
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90vw;
}

.q-card-section {
  padding: 16px 20px;
}

.q-card-actions {
  padding: 12px 20px 16px;
  gap: 8px;
}

/* 移动端适配 */
@media (max-width: 600px) {
  .shipping-methods-container {
    max-height: 50vh;
  }

  .shipping-method-item {
    padding: 10px;
    margin-bottom: 6px;
  }

  .shipping-main {
    flex-direction: column;
    gap: 4px;
  }

  .shipping-price {
    align-self: flex-end;
  }

  .shipping-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
